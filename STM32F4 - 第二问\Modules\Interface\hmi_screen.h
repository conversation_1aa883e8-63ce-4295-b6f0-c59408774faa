/**
  ******************************************************************************
  * @file    hmi_screen.h
  * <AUTHOR>
  * @version V1.0
  * @date    2025-01-01
  * @brief   串口触摸屏驱动模块 - 电赛G题专用
  *          支持参数设置、状态显示、波形显示等功能
  ******************************************************************************
  * @attention
  * 
  * 硬件连接：
  * - USART2_TX: PA2 → 串口屏RX
  * - USART2_RX: PA3 ← 串口屏TX
  * - 波特率: 9600 bps
  * - 数据格式: 8N1
  * 
  * 通信协议：
  * - 指令格式: 文本格式 + \xff\xff\xff结束符
  * - 支持字符串、数值、波形数据传输
  * - 双向通信，支持触摸事件接收
  *
  ******************************************************************************
  */

#ifndef __HMI_SCREEN_H
#define __HMI_SCREEN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  信号生成器参数结构体
  */
typedef struct {
    uint32_t frequency;          ///< 频率 (Hz)
    float amplitude;             ///< 峰峰值 (V)
    uint8_t wave_type;           ///< 波形类型 (0:正弦波, 1:方波, 2:三角波)
    uint8_t active_module;       ///< 活动模块 (0:DAC8552, 1:AD9834)
    bool output_enable;          ///< 输出使能
} SignalGen_Params_t;

/**
  * @brief  HMI屏幕状态结构体
  */
typedef struct {
    bool connected;              ///< 连接状态
    uint32_t last_update_time;   ///< 最后更新时间
    uint32_t tx_count;           ///< 发送计数
    uint32_t rx_count;           ///< 接收计数
    uint8_t rx_buffer[256];      ///< 接收缓冲区
    uint8_t rx_length;           ///< 接收长度
    bool rx_complete;            ///< 接收完成标志
} HMI_Status_t;

/**
  * @brief  HMI事件类型枚举
  */
typedef enum {
    HMI_EVENT_NONE = 0,          ///< 无事件
    HMI_EVENT_FREQ_SET,          ///< 频率设置
    HMI_EVENT_AMP_SET,           ///< 幅度设置
    HMI_EVENT_WAVE_SET,          ///< 波形设置
    HMI_EVENT_OUTPUT_TOGGLE,     ///< 输出开关
    HMI_EVENT_RESET,             ///< 复位
    HMI_EVENT_SWEEP_START,       ///< 开始扫频
    HMI_EVENT_SWEEP_STOP         ///< 停止扫频
} HMI_Event_t;

/**
  * @brief  HMI事件数据结构体
  */
typedef struct {
    HMI_Event_t event_type;      ///< 事件类型
    uint32_t param1;             ///< 参数1
    uint32_t param2;             ///< 参数2
    float param_float;           ///< 浮点参数
} HMI_EventData_t;

/* Exported constants --------------------------------------------------------*/

#define HMI_BAUDRATE                9600U        ///< 串口屏波特率
#define HMI_RX_BUFFER_SIZE          256U         ///< 接收缓冲区大小
#define HMI_TX_BUFFER_SIZE          512U         ///< 发送缓冲区大小
#define HMI_CMD_TIMEOUT_MS          1000U        ///< 命令超时时间

// 波形类型定义
#define HMI_WAVE_SINE               0            ///< 正弦波
#define HMI_WAVE_SQUARE             1            ///< 方波
#define HMI_WAVE_TRIANGLE           2            ///< 三角波

// 模块类型定义
#define HMI_MODULE_DAC8552          0            ///< DAC8552模块
#define HMI_MODULE_AD9834           1            ///< AD9834模块

// 频率范围定义
#define HMI_FREQ_MIN                100U         ///< 最小频率 (Hz)
#define HMI_FREQ_MAX                5000000U     ///< 最大频率 (Hz)

// 幅度范围定义
#define HMI_AMP_MIN                 1.0f         ///< 最小幅度 (V)
#define HMI_AMP_MAX                 5.0f         ///< 最大幅度 (V)

/* Exported variables --------------------------------------------------------*/
extern HMI_Status_t g_hmi_status;               ///< HMI状态
extern SignalGen_Params_t g_signal_params;     ///< 信号参数

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  HMI串口屏初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_Init(void);

/**
  * @brief  发送字符串到HMI屏幕
  * @param  component: 组件名称
  * @param  text: 文本内容
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendString(const char* component, const char* text);

/**
  * @brief  发送数值到HMI屏幕
  * @param  component: 组件名称
  * @param  value: 数值
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendNumber(const char* component, int32_t value);

/**
  * @brief  发送浮点数到HMI屏幕
  * @param  component: 组件名称
  * @param  value: 浮点数值
  * @param  precision: 精度 (小数位数)
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendFloat(const char* component, float value, uint8_t precision);

/**
  * @brief  更新信号参数显示
  * @param  params: 信号参数结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_UpdateSignalParams(const SignalGen_Params_t* params);

/**
  * @brief  更新系统状态显示
  * @param  status_text: 状态文本
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_UpdateStatus(const char* status_text);

/**
  * @brief  处理接收到的HMI事件
  * @param  None
  * @retval 事件数据指针，无事件时返回NULL
  */
HMI_EventData_t* HMI_ProcessEvents(void);

/**
  * @brief  HMI主循环处理函数
  * @param  None
  * @retval None
  */
void HMI_MainLoop(void);

/**
  * @brief  USART2中断处理函数
  * @param  None
  * @retval None
  */
void USART2_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* __HMI_SCREEN_H */

/************************ (C) COPYRIGHT STM32F4电赛项目组 *****END OF FILE****/
