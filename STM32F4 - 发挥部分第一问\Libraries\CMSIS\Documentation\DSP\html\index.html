<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CMSIS DSP Software Library</title>
<title>CMSIS-DSP: CMSIS DSP Software Library</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li class="current"><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('index.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">CMSIS DSP Software Library </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h2>Introduction</h2>
<p>This user manual describes the CMSIS DSP software library, a suite of common signal processing functions for use on Cortex-M processor based devices.</p>
<p>The library is divided into a number of functions each covering a specific category:</p>
<ul>
<li>Basic math functions</li>
<li>Fast math functions</li>
<li>Complex math functions</li>
<li>Filters</li>
<li>Matrix functions</li>
<li>Transforms</li>
<li>Motor control functions</li>
<li>Statistical functions</li>
<li>Support functions</li>
<li>Interpolation functions</li>
</ul>
<p>The library has separate functions for operating on 8-bit integers, 16-bit integers, 32-bit integer and 32-bit floating-point values.</p>
<h2>Using the Library</h2>
<p>The library installer contains prebuilt versions of the libraries in the <code>Lib</code> folder.</p>
<ul>
<li>arm_cortexM7lfdp_math.lib (Little endian and Double Precision Floating Point Unit on Cortex-M7)</li>
<li>arm_cortexM7bfdp_math.lib (Big endian and Double Precision Floating Point Unit on Cortex-M7)</li>
<li>arm_cortexM7lfsp_math.lib (Little endian and Single Precision Floating Point Unit on Cortex-M7)</li>
<li>arm_cortexM7bfsp_math.lib (Big endian and Single Precision Floating Point Unit on Cortex-M7)</li>
<li>arm_cortexM7l_math.lib (Little endian on Cortex-M7)</li>
<li>arm_cortexM7b_math.lib (Big endian on Cortex-M7)</li>
<li>arm_cortexM4lf_math.lib (Little endian and Floating Point Unit on Cortex-M4)</li>
<li>arm_cortexM4bf_math.lib (Big endian and Floating Point Unit on Cortex-M4)</li>
<li>arm_cortexM4l_math.lib (Little endian on Cortex-M4)</li>
<li>arm_cortexM4b_math.lib (Big endian on Cortex-M4)</li>
<li>arm_cortexM3l_math.lib (Little endian on Cortex-M3)</li>
<li>arm_cortexM3b_math.lib (Big endian on Cortex-M3)</li>
<li>arm_cortexM0l_math.lib (Little endian on Cortex-M0 / CortexM0+)</li>
<li>arm_cortexM0b_math.lib (Big endian on Cortex-M0 / CortexM0+)</li>
</ul>
<p>The library functions are declared in the public file <code><a class="el" href="arm__math_8h.html">arm_math.h</a></code> which is placed in the <code>Include</code> folder. Simply include this file and link the appropriate library in the application and begin calling the library functions. The Library supports single public header file <code> <a class="el" href="arm__math_8h.html">arm_math.h</a></code> for Cortex-M7/M4/M3/M0/M0+ with little endian and big endian. Same header file will be used for floating point unit(FPU) variants. Define the appropriate pre processor MACRO ARM_MATH_CM7 or ARM_MATH_CM4 or ARM_MATH_CM3 or ARM_MATH_CM0 or ARM_MATH_CM0PLUS depending on the target processor in the application.</p>
<h2>Examples</h2>
<p>The library ships with a number of examples which demonstrate how to use the library functions.</p>
<h2>Toolchain Support</h2>
<p>The library has been developed and tested with MDK-ARM version ******** The library is being tested in GCC and IAR toolchains and updates on this activity will be made available shortly.</p>
<h2>Building the Library</h2>
<p>The library installer contains a project file to re build libraries on MDK-ARM Tool chain in the <code>CMSIS\DSP_Lib\Source\ARM</code> folder.</p>
<ul>
<li>arm_cortexM_math.uvprojx</li>
</ul>
<p>The libraries can be built by opening the arm_cortexM_math.uvprojx project in MDK-ARM, selecting a specific target, and defining the optional pre processor MACROs detailed above.</p>
<h2>Pre-processor Macros</h2>
<p>Each library project have differant pre-processor macros.</p>
<ul>
<li>UNALIGNED_SUPPORT_DISABLE:</li>
</ul>
<p>Define macro UNALIGNED_SUPPORT_DISABLE, If the silicon does not support unaligned memory access</p>
<ul>
<li>ARM_MATH_BIG_ENDIAN:</li>
</ul>
<p>Define macro ARM_MATH_BIG_ENDIAN to build the library for big endian targets. By default library builds for little endian targets.</p>
<ul>
<li>ARM_MATH_MATRIX_CHECK:</li>
</ul>
<p>Define macro ARM_MATH_MATRIX_CHECK for checking on the input and output sizes of matrices</p>
<ul>
<li>ARM_MATH_ROUNDING:</li>
</ul>
<p>Define macro ARM_MATH_ROUNDING for rounding on support functions</p>
<ul>
<li>ARM_MATH_CMx:</li>
</ul>
<p>Define macro ARM_MATH_CM4 for building the library on Cortex-M4 target, ARM_MATH_CM3 for building library on Cortex-M3 target and ARM_MATH_CM0 for building library on Cortex-M0 target, ARM_MATH_CM0PLUS for building library on Cortex-M0+ target, and ARM_MATH_CM7 for building the library on cortex-M7.</p>
<ul>
<li>__FPU_PRESENT:</li>
</ul>
<p>Initialize macro __FPU_PRESENT = 1 when building on FPU supported Targets. Enable this macro for M4bf and M4lf libraries</p>
<hr/>
 <h2>CMSIS-DSP in ARM::CMSIS Pack</h2>
<p>The following files relevant to CMSIS-DSP are present in the <b>ARM::CMSIS</b> Pack directories: </p>
<table class="doxtable">
<tr>
<th>File/Folder </th><th>Content </th></tr>
<tr>
<td><b>CMSIS\Documentation\DSP</b> </td><td>This documentation </td></tr>
<tr>
<td><b>CMSIS\DSP_Lib</b> </td><td>Software license agreement (license.txt) </td></tr>
<tr>
<td><b>CMSIS\DSP_Lib\Examples</b> </td><td>Example projects demonstrating the usage of the library functions </td></tr>
<tr>
<td><b>CMSIS\DSP_Lib\Source</b> </td><td>Source files for rebuilding the library </td></tr>
</table>
<hr/>
 <h2>Revision History of CMSIS-DSP</h2>
<p>Please refer to <a class="el" href="_change_log_pg.html">Change Log</a>.</p>
<h2>Copyright Notice</h2>
<p>Copyright (C) 2010-2015 ARM Limited. All rights reserved. </p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
