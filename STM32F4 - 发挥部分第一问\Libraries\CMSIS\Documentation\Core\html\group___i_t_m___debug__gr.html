<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Debug Access</title>
<title>CMSIS-CORE: Debug Access</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___i_t_m___debug__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">Debug Access</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaaa7c716331f74d644bf6bf25cd3392d1"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_t_m___debug__gr.html#gaaa7c716331f74d644bf6bf25cd3392d1">ITM_SendChar</a> (uint32_t ch)</td></tr>
<tr class="memdesc:gaaa7c716331f74d644bf6bf25cd3392d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transmits a character via channel 0.  <a href="#gaaa7c716331f74d644bf6bf25cd3392d1"></a><br/></td></tr>
<tr class="separator:gaaa7c716331f74d644bf6bf25cd3392d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37b8f41cae703b5ff6947e271065558c"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_t_m___debug__gr.html#ga37b8f41cae703b5ff6947e271065558c">ITM_ReceiveChar</a> (void)</td></tr>
<tr class="memdesc:ga37b8f41cae703b5ff6947e271065558c"><td class="mdescLeft">&#160;</td><td class="mdescRight">ITM Receive Character.  <a href="#ga37b8f41cae703b5ff6947e271065558c"></a><br/></td></tr>
<tr class="separator:ga37b8f41cae703b5ff6947e271065558c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f9bbabd9756d1a7eafb2d9bf27e0535"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_t_m___debug__gr.html#ga7f9bbabd9756d1a7eafb2d9bf27e0535">ITM_CheckChar</a> (void)</td></tr>
<tr class="memdesc:ga7f9bbabd9756d1a7eafb2d9bf27e0535"><td class="mdescLeft">&#160;</td><td class="mdescRight">ITM Check Character.  <a href="#ga7f9bbabd9756d1a7eafb2d9bf27e0535"></a><br/></td></tr>
<tr class="separator:ga7f9bbabd9756d1a7eafb2d9bf27e0535"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ga12e68e55a7badc271b948d6c7230b2a8"><td class="memItemLeft" align="right" valign="top">volatile int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a></td></tr>
<tr class="memdesc:ga12e68e55a7badc271b948d6c7230b2a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">external variable to receive characters  <a href="#ga12e68e55a7badc271b948d6c7230b2a8"></a><br/></td></tr>
<tr class="separator:ga12e68e55a7badc271b948d6c7230b2a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>CMSIS provides additional debug functions to enlarge the Debug Access. Data can be transmitted via a certain global buffer variable towards the target system.</p>
<p>The Cortex-M3 / Cortex-M4 / Cortex-M7 incorporates the <b>Instrumented Trace Macrocell (ITM)</b> that provides together with the <b>Serial Viewer Output (SVO)</b> trace capabilities for the microcontroller system. The ITM has 32 communication channels; two ITM communication channels are used by CMSIS to output the following information:</p>
<ul>
<li><b>ITM Channel 0</b>: implements the <a class="el" href="group___i_t_m___debug__gr.html#gaaa7c716331f74d644bf6bf25cd3392d1">ITM_SendChar</a> function which can be used for printf-style output via the debug interface.</li>
</ul>
<ul>
<li><b>ITM Channel 31</b>: is reserved for the RTOS kernel and can be used for kernel awareness debugging.</li>
</ul>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>ITM channels have 4 groups with 8 channels each, whereby each group can be configured for access rights in the Unprivileged level.</li>
<li>The ITM channel 0 can be enabled for the user task.</li>
<li>ITM channel 31 can be accessed only in Privileged mode from the RTOS kernel itself. The ITM channel 31 has been selected for the RTOS kernel because some kernels may use the Privileged level for program execution.</li>
</ul>
</dd></dl>
<hr/>
 <h1><a class="anchor" id="ITM_debug_uv"></a>
ITM Debug Support in uVision</h1>
<p>In a debug session, uVision uses the <b>Debug (printf) Viewer</b> window to display data.</p>
<p><b>Direction: Microcontroller &ndash;&gt; uVision:</b></p>
<ul>
<li>Characters received via ITM communication channel 0 are written in a printf-style to the <b>Debug (printf) Viewer</b> window.</li>
</ul>
<p><b>Direction: uVision &ndash;&gt; Microcontroller:</b></p>
<ul>
<li>Check if <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a> variable is available (only performed once).</li>
<li>Read the character from the <b>Debug (printf) Viewer</b> window.</li>
<li>If <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a> is empty, write character to <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a>.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>The current solution does not use a buffer mechanism for transmitting the characters.</dd></dl>
<hr/>
 <h1><a class="anchor" id="itm_debug_ex"></a>
Example:</h1>
<p>Example for the usage of the ITM Channel 31 for RTOS Kernels:</p>
<div class="fragment"><div class="line"><span class="comment">// check if debugger connected and ITM channel enabled for tracing</span></div>
<div class="line"><span class="keywordflow">if</span> ((CoreDebug-&gt;DEMCR &amp; CoreDebug_DEMCR_TRCENA) &amp;&amp;</div>
<div class="line">    (ITM-&gt;TCR &amp; ITM_TCR_ITMENA) &amp;&amp;</div>
<div class="line">    (ITM-&gt;TER &amp; (1UL &gt;&gt; 31))) {</div>
<div class="line">    </div>
<div class="line">    <span class="comment">// transmit trace data</span></div>
<div class="line">    <span class="keywordflow">while</span> (ITM-&gt;PORT31_U32 == 0);</div>
<div class="line">    ITM-&gt;PORT[31].u8 = task_id;      <span class="comment">// id of next task</span></div>
<div class="line">    <span class="keywordflow">while</span> (ITM-&gt;PORT[31].u32 == 0);</div>
<div class="line">    ITM-&gt;PORT[31].u32 = task_status; <span class="comment">// status information</span></div>
<div class="line">}</div>
</div><!-- fragment --> <h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga7f9bbabd9756d1a7eafb2d9bf27e0535"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ITM_CheckChar </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reads the external variable <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a> and checks whether a character is available or not.</p>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>=0 - No character available</li>
<li>=1 - Character available </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga37b8f41cae703b5ff6947e271065558c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t ITM_ReceiveChar </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function inputs a character via the external variable <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">ITM_RxBuffer</a>. It returns when no debugger is connected that has booked the output. It is blocking when a debugger is connected, but the previously sent character has not been transmitted.</p>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>Received character</li>
<li>=1 - No character received </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gaaa7c716331f74d644bf6bf25cd3392d1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ITM_SendChar </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ch</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function transmits a character via the ITM channel 0. It returns when no debugger is connected that has booked the output. It is blocking when a debugger is connected, but the previously sent character has not been transmitted.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ch</td><td>Character to transmit</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Character to transmit </dd></dl>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ga12e68e55a7badc271b948d6c7230b2a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">volatile int32_t ITM_RxBuffer</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
