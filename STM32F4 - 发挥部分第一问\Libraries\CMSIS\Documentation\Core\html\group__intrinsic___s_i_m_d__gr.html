<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]</title>
<title>CMSIS-CORE: Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__intrinsic___s_i_m_d__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Intrinsic Functions for SIMD Instructions [only Cortex-M4 and Cortex-M7]</div>  </div>
</div><!--header-->
<div class="contents">

<p>Access to dedicated SIMD instructions.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gac20aa0f741d0a1494d58c531e38d5785"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac20aa0f741d0a1494d58c531e38d5785">__SADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gac20aa0f741d0a1494d58c531e38d5785"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting quad 8-bit signed addition.  <a href="#gac20aa0f741d0a1494d58c531e38d5785"></a><br/></td></tr>
<tr class="separator:gac20aa0f741d0a1494d58c531e38d5785"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2f5a9132dcfc6d01d34cd971c425713"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf2f5a9132dcfc6d01d34cd971c425713">__QADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gaf2f5a9132dcfc6d01d34cd971c425713"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting quad 8-bit saturating addition.  <a href="#gaf2f5a9132dcfc6d01d34cd971c425713"></a><br/></td></tr>
<tr class="separator:gaf2f5a9132dcfc6d01d34cd971c425713"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga524575b442ea01aec10c762bf4d85fea"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga524575b442ea01aec10c762bf4d85fea">__SHADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga524575b442ea01aec10c762bf4d85fea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit signed addition with halved results.  <a href="#ga524575b442ea01aec10c762bf4d85fea"></a><br/></td></tr>
<tr class="separator:ga524575b442ea01aec10c762bf4d85fea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3d7fd00d113b20fb3741a17394da762"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab3d7fd00d113b20fb3741a17394da762">__UADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gab3d7fd00d113b20fb3741a17394da762"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting quad 8-bit unsigned addition.  <a href="#gab3d7fd00d113b20fb3741a17394da762"></a><br/></td></tr>
<tr class="separator:gab3d7fd00d113b20fb3741a17394da762"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa9af218db3934a692fb06fa728d8031"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafa9af218db3934a692fb06fa728d8031">__UQADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gafa9af218db3934a692fb06fa728d8031"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit unsigned saturating addition.  <a href="#gafa9af218db3934a692fb06fa728d8031"></a><br/></td></tr>
<tr class="separator:gafa9af218db3934a692fb06fa728d8031"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a14e5485e59bf0f23595b7c2a94eb0b"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga3a14e5485e59bf0f23595b7c2a94eb0b">__UHADD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga3a14e5485e59bf0f23595b7c2a94eb0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit unsigned addition with halved results.  <a href="#ga3a14e5485e59bf0f23595b7c2a94eb0b"></a><br/></td></tr>
<tr class="separator:ga3a14e5485e59bf0f23595b7c2a94eb0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba63bb52e1e93fb527e26f3d474da12e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaba63bb52e1e93fb527e26f3d474da12e">__SSUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gaba63bb52e1e93fb527e26f3d474da12e"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting quad 8-bit signed subtraction.  <a href="#gaba63bb52e1e93fb527e26f3d474da12e"></a><br/></td></tr>
<tr class="separator:gaba63bb52e1e93fb527e26f3d474da12e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga753493a65493880c28baa82c151a0d61"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga753493a65493880c28baa82c151a0d61">__QSUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga753493a65493880c28baa82c151a0d61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting quad 8-bit saturating subtract.  <a href="#ga753493a65493880c28baa82c151a0d61"></a><br/></td></tr>
<tr class="separator:ga753493a65493880c28baa82c151a0d61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3ec7215b354d925a239f3b31df2b77b"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac3ec7215b354d925a239f3b31df2b77b">__SHSUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gac3ec7215b354d925a239f3b31df2b77b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit signed subtraction with halved results.  <a href="#gac3ec7215b354d925a239f3b31df2b77b"></a><br/></td></tr>
<tr class="separator:gac3ec7215b354d925a239f3b31df2b77b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb7257dc3b8e9acbd0ef0e31ff87d4b8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gacb7257dc3b8e9acbd0ef0e31ff87d4b8">__USUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gacb7257dc3b8e9acbd0ef0e31ff87d4b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting quad 8-bit unsigned subtract.  <a href="#gacb7257dc3b8e9acbd0ef0e31ff87d4b8"></a><br/></td></tr>
<tr class="separator:gacb7257dc3b8e9acbd0ef0e31ff87d4b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9736fe816aec74fe886e7fb949734eab"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9736fe816aec74fe886e7fb949734eab">__UQSUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga9736fe816aec74fe886e7fb949734eab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit unsigned saturating subtraction.  <a href="#ga9736fe816aec74fe886e7fb949734eab"></a><br/></td></tr>
<tr class="separator:ga9736fe816aec74fe886e7fb949734eab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga48a55df1c3e73923b73819d7c19b392d"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga48a55df1c3e73923b73819d7c19b392d">__UHSUB8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga48a55df1c3e73923b73819d7c19b392d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quad 8-bit unsigned subtraction with halved results.  <a href="#ga48a55df1c3e73923b73819d7c19b392d"></a><br/></td></tr>
<tr class="separator:ga48a55df1c3e73923b73819d7c19b392d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0bf46373a1c05aabf64517e84be5984"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad0bf46373a1c05aabf64517e84be5984">__SADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gad0bf46373a1c05aabf64517e84be5984"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit signed addition.  <a href="#gad0bf46373a1c05aabf64517e84be5984"></a><br/></td></tr>
<tr class="separator:gad0bf46373a1c05aabf64517e84be5984"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae83a53ec04b496304bed6d9fe8f7461b"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae83a53ec04b496304bed6d9fe8f7461b">__QADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gae83a53ec04b496304bed6d9fe8f7461b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit saturating addition.  <a href="#gae83a53ec04b496304bed6d9fe8f7461b"></a><br/></td></tr>
<tr class="separator:gae83a53ec04b496304bed6d9fe8f7461b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15d8899a173effb8ad8c7268da32b60e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga15d8899a173effb8ad8c7268da32b60e">__SHADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga15d8899a173effb8ad8c7268da32b60e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed addition with halved results.  <a href="#ga15d8899a173effb8ad8c7268da32b60e"></a><br/></td></tr>
<tr class="separator:ga15d8899a173effb8ad8c7268da32b60e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa1160f0cf76d6aa292fbad54a1aa6b74"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaa1160f0cf76d6aa292fbad54a1aa6b74">__UADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gaa1160f0cf76d6aa292fbad54a1aa6b74"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit unsigned addition.  <a href="#gaa1160f0cf76d6aa292fbad54a1aa6b74"></a><br/></td></tr>
<tr class="separator:gaa1160f0cf76d6aa292fbad54a1aa6b74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e2cc5117e79578a08b25f1e89022966"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9e2cc5117e79578a08b25f1e89022966">__UQADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga9e2cc5117e79578a08b25f1e89022966"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned saturating addition.  <a href="#ga9e2cc5117e79578a08b25f1e89022966"></a><br/></td></tr>
<tr class="separator:ga9e2cc5117e79578a08b25f1e89022966"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd0b0e2da2e6364e176d051687702b86"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gabd0b0e2da2e6364e176d051687702b86">__UHADD16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gabd0b0e2da2e6364e176d051687702b86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned addition with halved results.  <a href="#gabd0b0e2da2e6364e176d051687702b86"></a><br/></td></tr>
<tr class="separator:gabd0b0e2da2e6364e176d051687702b86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4262f73be75efbac6b46ab7c71aa6cbc"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga4262f73be75efbac6b46ab7c71aa6cbc">__SSUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga4262f73be75efbac6b46ab7c71aa6cbc"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit signed subtraction.  <a href="#ga4262f73be75efbac6b46ab7c71aa6cbc"></a><br/></td></tr>
<tr class="separator:ga4262f73be75efbac6b46ab7c71aa6cbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad089605c16df9823a2c8aaa37777aae5"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad089605c16df9823a2c8aaa37777aae5">__QSUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gad089605c16df9823a2c8aaa37777aae5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit saturating subtract.  <a href="#gad089605c16df9823a2c8aaa37777aae5"></a><br/></td></tr>
<tr class="separator:gad089605c16df9823a2c8aaa37777aae5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31328467f0f91b8ff9ae9a01682ad3bf"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga31328467f0f91b8ff9ae9a01682ad3bf">__SHSUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga31328467f0f91b8ff9ae9a01682ad3bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed subtraction with halved results.  <a href="#ga31328467f0f91b8ff9ae9a01682ad3bf"></a><br/></td></tr>
<tr class="separator:ga31328467f0f91b8ff9ae9a01682ad3bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f2b77e11fc4a77b26c36c423ed45b4e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9f2b77e11fc4a77b26c36c423ed45b4e">__USUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga9f2b77e11fc4a77b26c36c423ed45b4e"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit unsigned subtract.  <a href="#ga9f2b77e11fc4a77b26c36c423ed45b4e"></a><br/></td></tr>
<tr class="separator:ga9f2b77e11fc4a77b26c36c423ed45b4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ec4e2e231d15e5c692233feb3806187"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5ec4e2e231d15e5c692233feb3806187">__UQSUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga5ec4e2e231d15e5c692233feb3806187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned saturating subtraction.  <a href="#ga5ec4e2e231d15e5c692233feb3806187"></a><br/></td></tr>
<tr class="separator:ga5ec4e2e231d15e5c692233feb3806187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f7545b8dc33bb97982731cb9d427a69"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga1f7545b8dc33bb97982731cb9d427a69">__UHSUB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga1f7545b8dc33bb97982731cb9d427a69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned subtraction with halved results.  <a href="#ga1f7545b8dc33bb97982731cb9d427a69"></a><br/></td></tr>
<tr class="separator:ga1f7545b8dc33bb97982731cb9d427a69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5845084fd99c872e98cf5553d554de2a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5845084fd99c872e98cf5553d554de2a">__SASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga5845084fd99c872e98cf5553d554de2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit addition and subtraction with exchange.  <a href="#ga5845084fd99c872e98cf5553d554de2a"></a><br/></td></tr>
<tr class="separator:ga5845084fd99c872e98cf5553d554de2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87618799672e1511e33964bc71467eb3"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga87618799672e1511e33964bc71467eb3">__QASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga87618799672e1511e33964bc71467eb3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit add and subtract with exchange.  <a href="#ga87618799672e1511e33964bc71467eb3"></a><br/></td></tr>
<tr class="separator:ga87618799672e1511e33964bc71467eb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0a649035f67627464fd80e7218c89d5"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae0a649035f67627464fd80e7218c89d5">__SHASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gae0a649035f67627464fd80e7218c89d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed addition and subtraction with halved results.  <a href="#gae0a649035f67627464fd80e7218c89d5"></a><br/></td></tr>
<tr class="separator:gae0a649035f67627464fd80e7218c89d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga980353d2c72ebb879282e49f592fddc0"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga980353d2c72ebb879282e49f592fddc0">__UASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga980353d2c72ebb879282e49f592fddc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit unsigned addition and subtraction with exchange.  <a href="#ga980353d2c72ebb879282e49f592fddc0"></a><br/></td></tr>
<tr class="separator:ga980353d2c72ebb879282e49f592fddc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5eff3ae5eabcd73f3049996ca391becb"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5eff3ae5eabcd73f3049996ca391becb">__UQASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga5eff3ae5eabcd73f3049996ca391becb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned saturating addition and subtraction with exchange.  <a href="#ga5eff3ae5eabcd73f3049996ca391becb"></a><br/></td></tr>
<tr class="separator:ga5eff3ae5eabcd73f3049996ca391becb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga028f0732b961fb6e5209326fb3855261"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga028f0732b961fb6e5209326fb3855261">__UHASX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga028f0732b961fb6e5209326fb3855261"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned addition and subtraction with halved results and exchange.  <a href="#ga028f0732b961fb6e5209326fb3855261"></a><br/></td></tr>
<tr class="separator:ga028f0732b961fb6e5209326fb3855261"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d3bc5c539f9bd50f7d59ffa37ac6a65"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9d3bc5c539f9bd50f7d59ffa37ac6a65">__SSAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga9d3bc5c539f9bd50f7d59ffa37ac6a65"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit signed subtraction and addition with exchange.  <a href="#ga9d3bc5c539f9bd50f7d59ffa37ac6a65"></a><br/></td></tr>
<tr class="separator:ga9d3bc5c539f9bd50f7d59ffa37ac6a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab41eb2b17512ab01d476fc9d5bd19520"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab41eb2b17512ab01d476fc9d5bd19520">__QSAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gab41eb2b17512ab01d476fc9d5bd19520"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit subtract and add with exchange.  <a href="#gab41eb2b17512ab01d476fc9d5bd19520"></a><br/></td></tr>
<tr class="separator:gab41eb2b17512ab01d476fc9d5bd19520"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafadbd89c36b5addcf1ca10dd392db3e9"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafadbd89c36b5addcf1ca10dd392db3e9">__SHSAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gafadbd89c36b5addcf1ca10dd392db3e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed subtraction and addition with halved results.  <a href="#gafadbd89c36b5addcf1ca10dd392db3e9"></a><br/></td></tr>
<tr class="separator:gafadbd89c36b5addcf1ca10dd392db3e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga578a082747436772c482c96d7a58e45e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga578a082747436772c482c96d7a58e45e">__USAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga578a082747436772c482c96d7a58e45e"><td class="mdescLeft">&#160;</td><td class="mdescRight">GE setting dual 16-bit unsigned subtract and add with exchange.  <a href="#ga578a082747436772c482c96d7a58e45e"></a><br/></td></tr>
<tr class="separator:ga578a082747436772c482c96d7a58e45e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadecfdfabc328d8939d49d996f2fd4482"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gadecfdfabc328d8939d49d996f2fd4482">__UQSAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gadecfdfabc328d8939d49d996f2fd4482"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned saturating subtraction and addition with exchange.  <a href="#gadecfdfabc328d8939d49d996f2fd4482"></a><br/></td></tr>
<tr class="separator:gadecfdfabc328d8939d49d996f2fd4482"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09e129e6613329aab87c89f1108b7ed7"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga09e129e6613329aab87c89f1108b7ed7">__UHSAX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga09e129e6613329aab87c89f1108b7ed7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit unsigned subtraction and addition with halved results and exchange.  <a href="#ga09e129e6613329aab87c89f1108b7ed7"></a><br/></td></tr>
<tr class="separator:ga09e129e6613329aab87c89f1108b7ed7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac8855c07044239ea775c8128013204f0"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac8855c07044239ea775c8128013204f0">__USAD8</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gac8855c07044239ea775c8128013204f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned sum of quad 8-bit unsigned absolute difference.  <a href="#gac8855c07044239ea775c8128013204f0"></a><br/></td></tr>
<tr class="separator:gac8855c07044239ea775c8128013204f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad032bd21f013c5d29f5fcb6b0f02bc3f"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad032bd21f013c5d29f5fcb6b0f02bc3f">__USADA8</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:gad032bd21f013c5d29f5fcb6b0f02bc3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned sum of quad 8-bit unsigned absolute difference with 32-bit accumulate.  <a href="#gad032bd21f013c5d29f5fcb6b0f02bc3f"></a><br/></td></tr>
<tr class="separator:gad032bd21f013c5d29f5fcb6b0f02bc3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga95e666b82216066bf6064d1244e6883c"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga95e666b82216066bf6064d1244e6883c">__SSAT16</a> (uint32_t val1, const uint32_t val2)</td></tr>
<tr class="memdesc:ga95e666b82216066bf6064d1244e6883c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit saturate.  <a href="#ga95e666b82216066bf6064d1244e6883c"></a><br/></td></tr>
<tr class="separator:ga95e666b82216066bf6064d1244e6883c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga967f516afff5900cf30f1a81907cdd89"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga967f516afff5900cf30f1a81907cdd89">__USAT16</a> (uint32_t val1, const uint32_t val2)</td></tr>
<tr class="memdesc:ga967f516afff5900cf30f1a81907cdd89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit unsigned saturate.  <a href="#ga967f516afff5900cf30f1a81907cdd89"></a><br/></td></tr>
<tr class="separator:ga967f516afff5900cf30f1a81907cdd89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab41d713653b16f8d9fef44d14e397228"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab41d713653b16f8d9fef44d14e397228">__UXTB16</a> (uint32_t val)</td></tr>
<tr class="memdesc:gab41d713653b16f8d9fef44d14e397228"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual extract 8-bits and zero-extend to 16-bits.  <a href="#gab41d713653b16f8d9fef44d14e397228"></a><br/></td></tr>
<tr class="separator:gab41d713653b16f8d9fef44d14e397228"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad25ce96db0f17096bbd815f4817faf09"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad25ce96db0f17096bbd815f4817faf09">__UXTAB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gad25ce96db0f17096bbd815f4817faf09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracted 16-bit to 32-bit unsigned addition.  <a href="#gad25ce96db0f17096bbd815f4817faf09"></a><br/></td></tr>
<tr class="separator:gad25ce96db0f17096bbd815f4817faf09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga38dce3dd13ba212e80ec3cff4abeb11a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga38dce3dd13ba212e80ec3cff4abeb11a">__SXTB16</a> (uint32_t val)</td></tr>
<tr class="memdesc:ga38dce3dd13ba212e80ec3cff4abeb11a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual extract 8-bits and sign extend each to 16-bits.  <a href="#ga38dce3dd13ba212e80ec3cff4abeb11a"></a><br/></td></tr>
<tr class="separator:ga38dce3dd13ba212e80ec3cff4abeb11a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac540b4fc41d30778ba102d2a65db5589"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac540b4fc41d30778ba102d2a65db5589">__SXTAB16</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gac540b4fc41d30778ba102d2a65db5589"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual extracted 8-bit to 16-bit signed addition.  <a href="#gac540b4fc41d30778ba102d2a65db5589"></a><br/></td></tr>
<tr class="separator:gac540b4fc41d30778ba102d2a65db5589"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae326e368a1624d2dfb4b97c626939257"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae326e368a1624d2dfb4b97c626939257">__SMUAD</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gae326e368a1624d2dfb4b97c626939257"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting sum of dual 16-bit signed multiply.  <a href="#gae326e368a1624d2dfb4b97c626939257"></a><br/></td></tr>
<tr class="separator:gae326e368a1624d2dfb4b97c626939257"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee6390f86965cb662500f690b0012092"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaee6390f86965cb662500f690b0012092">__SMUADX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gaee6390f86965cb662500f690b0012092"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting sum of dual 16-bit signed multiply with exchange.  <a href="#gaee6390f86965cb662500f690b0012092"></a><br/></td></tr>
<tr class="separator:gaee6390f86965cb662500f690b0012092"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaea60757232f740ec6b09980eebb614ff"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaea60757232f740ec6b09980eebb614ff">__SMMLA</a> (int32_t val1, int32_t val2, int32_t val3)</td></tr>
<tr class="memdesc:gaea60757232f740ec6b09980eebb614ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">32-bit signed multiply with 32-bit truncated accumulator.  <a href="#gaea60757232f740ec6b09980eebb614ff"></a><br/></td></tr>
<tr class="separator:gaea60757232f740ec6b09980eebb614ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae0c86f3298532183f3a29f5bb454d354"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae0c86f3298532183f3a29f5bb454d354">__SMLAD</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:gae0c86f3298532183f3a29f5bb454d354"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit signed multiply with single 32-bit accumulator.  <a href="#gae0c86f3298532183f3a29f5bb454d354"></a><br/></td></tr>
<tr class="separator:gae0c86f3298532183f3a29f5bb454d354"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c286d330f4fb29b256335add91eec9f"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9c286d330f4fb29b256335add91eec9f">__SMLADX</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:ga9c286d330f4fb29b256335add91eec9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting pre-exchanged dual 16-bit signed multiply with single 32-bit accumulator.  <a href="#ga9c286d330f4fb29b256335add91eec9f"></a><br/></td></tr>
<tr class="separator:ga9c286d330f4fb29b256335add91eec9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad80e9b20c1736fd798f897362273a146"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad80e9b20c1736fd798f897362273a146">__SMLALD</a> (uint32_t val1, uint32_t val2, uint64_t val3)</td></tr>
<tr class="memdesc:gad80e9b20c1736fd798f897362273a146"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed multiply with single 64-bit accumulator.  <a href="#gad80e9b20c1736fd798f897362273a146"></a><br/></td></tr>
<tr class="separator:gad80e9b20c1736fd798f897362273a146"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1adad1b3f2667328cc0db6c6b4f41cf"><td class="memItemLeft" align="right" valign="top">unsigned long long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad1adad1b3f2667328cc0db6c6b4f41cf">__SMLALDX</a> (uint32_t val1, uint32_t val2, unsigned long long val3)</td></tr>
<tr class="memdesc:gad1adad1b3f2667328cc0db6c6b4f41cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed multiply with exchange with single 64-bit accumulator.  <a href="#gad1adad1b3f2667328cc0db6c6b4f41cf"></a><br/></td></tr>
<tr class="separator:gad1adad1b3f2667328cc0db6c6b4f41cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga039142a5368840683cf329cb55b73f84"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga039142a5368840683cf329cb55b73f84">__SMUSD</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga039142a5368840683cf329cb55b73f84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed multiply returning difference.  <a href="#ga039142a5368840683cf329cb55b73f84"></a><br/></td></tr>
<tr class="separator:ga039142a5368840683cf329cb55b73f84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb5bcba694bf17b141c32e6a8474f60e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gabb5bcba694bf17b141c32e6a8474f60e">__SMUSDX</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gabb5bcba694bf17b141c32e6a8474f60e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dual 16-bit signed multiply with exchange returning difference.  <a href="#gabb5bcba694bf17b141c32e6a8474f60e"></a><br/></td></tr>
<tr class="separator:gabb5bcba694bf17b141c32e6a8474f60e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4350af7f2030c36f43b2c104a9d16cd"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf4350af7f2030c36f43b2c104a9d16cd">__SMLSD</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:gaf4350af7f2030c36f43b2c104a9d16cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit signed multiply subtract with 32-bit accumulate.  <a href="#gaf4350af7f2030c36f43b2c104a9d16cd"></a><br/></td></tr>
<tr class="separator:gaf4350af7f2030c36f43b2c104a9d16cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5290ce5564770ad124910d2583dc0a9e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5290ce5564770ad124910d2583dc0a9e">__SMLSDX</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:ga5290ce5564770ad124910d2583dc0a9e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit signed multiply with exchange subtract with 32-bit accumulate.  <a href="#ga5290ce5564770ad124910d2583dc0a9e"></a><br/></td></tr>
<tr class="separator:ga5290ce5564770ad124910d2583dc0a9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5611f7314e0c8f53da377918dfbf42ee"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5611f7314e0c8f53da377918dfbf42ee">__SMLSLD</a> (uint32_t val1, uint32_t val2, uint64_t val3)</td></tr>
<tr class="memdesc:ga5611f7314e0c8f53da377918dfbf42ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit signed multiply subtract with 64-bit accumulate.  <a href="#ga5611f7314e0c8f53da377918dfbf42ee"></a><br/></td></tr>
<tr class="separator:ga5611f7314e0c8f53da377918dfbf42ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83e69ef81057d3cbd06863d729385187"><td class="memItemLeft" align="right" valign="top">unsigned long long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga83e69ef81057d3cbd06863d729385187">__SMLSLDX</a> (uint32_t val1, uint32_t val2, unsigned long long val3)</td></tr>
<tr class="memdesc:ga83e69ef81057d3cbd06863d729385187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting dual 16-bit signed multiply with exchange subtract with 64-bit accumulate.  <a href="#ga83e69ef81057d3cbd06863d729385187"></a><br/></td></tr>
<tr class="separator:ga83e69ef81057d3cbd06863d729385187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5448e591fe49161b6759b48aecb08fe"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf5448e591fe49161b6759b48aecb08fe">__SEL</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:gaf5448e591fe49161b6759b48aecb08fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Select bytes based on GE bits.  <a href="#gaf5448e591fe49161b6759b48aecb08fe"></a><br/></td></tr>
<tr class="separator:gaf5448e591fe49161b6759b48aecb08fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17b873f246c9f5e9355760ffef3dad4a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga17b873f246c9f5e9355760ffef3dad4a">__QADD</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga17b873f246c9f5e9355760ffef3dad4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting saturating add.  <a href="#ga17b873f246c9f5e9355760ffef3dad4a"></a><br/></td></tr>
<tr class="separator:ga17b873f246c9f5e9355760ffef3dad4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ba259f8f05a36f7b88b469a71ffc096"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga3ba259f8f05a36f7b88b469a71ffc096">__QSUB</a> (uint32_t val1, uint32_t val2)</td></tr>
<tr class="memdesc:ga3ba259f8f05a36f7b88b469a71ffc096"><td class="mdescLeft">&#160;</td><td class="mdescRight">Q setting saturating subtract.  <a href="#ga3ba259f8f05a36f7b88b469a71ffc096"></a><br/></td></tr>
<tr class="separator:ga3ba259f8f05a36f7b88b469a71ffc096"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefb8ebf3a54e197464da1ff69a44f4b5"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaefb8ebf3a54e197464da1ff69a44f4b5">__PKHBT</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:gaefb8ebf3a54e197464da1ff69a44f4b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Halfword packing instruction. Combines bits[15:0] of <em>val1</em> with bits[31:16] of <em>val2</em> levitated with the <em>val3</em>.  <a href="#gaefb8ebf3a54e197464da1ff69a44f4b5"></a><br/></td></tr>
<tr class="separator:gaefb8ebf3a54e197464da1ff69a44f4b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd8fe4a6d87e947caa81a69ec36c1666"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafd8fe4a6d87e947caa81a69ec36c1666">__PKHTB</a> (uint32_t val1, uint32_t val2, uint32_t val3)</td></tr>
<tr class="memdesc:gafd8fe4a6d87e947caa81a69ec36c1666"><td class="mdescLeft">&#160;</td><td class="mdescRight">Halfword packing instruction. Combines bits[31:16] of <em>val1</em> with bits[15:0] of <em>val2</em> right-shifted with the <em>val3</em>.  <a href="#gafd8fe4a6d87e947caa81a69ec36c1666"></a><br/></td></tr>
<tr class="separator:gafd8fe4a6d87e947caa81a69ec36c1666"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p><b>Single Instruction Multiple Data (SIMD)</b> extensions are provided <b>only for Cortex-M4 and Cortex-M7 cores</b> to simplify development of application software. SIMD extensions increase the processing capability without materially increasing the power consumption. The SIMD extensions are completely transparent to the operating system (OS), allowing existing OS ports to be used.</p>
<p><b>SIMD Features:</b></p>
<ul>
<li>Simultaneous computation of 2x16-bit or 4x8-bit operands</li>
<li>Fractional arithmetic</li>
<li>User definable saturation modes (arbitrary word-width)</li>
<li>Dual 16x16 multiply-add/subtract 32x32 fractional MAC</li>
<li>Simultaneous 8/16-bit select operations</li>
<li>Performance up to 3.2 GOPS at 800MHz</li>
<li>Performance is achieved with a "near zero" increase in power consumption on a typical implementation</li>
</ul>
<p><b>Examples:</b> </p>
<p><b>Addition:</b> Add two values using SIMD function</p>
<div class="fragment"><div class="line">uint32_t add_halfwords(uint32_t val1, uint32_t val2)</div>
<div class="line">{</div>
<div class="line">  <span class="keywordflow">return</span> <a class="code" href="group__intrinsic___s_i_m_d__gr.html#gad0bf46373a1c05aabf64517e84be5984" title="GE setting dual 16-bit signed addition.">__SADD16</a>(val1, val2);</div>
<div class="line">}</div>
</div><!-- fragment --><p><b>Subtraction:</b> Subtract two values using SIMD function</p>
<div class="fragment"><div class="line">uint32_t sub_halfwords(uint32_t val1, uint32_t val2)</div>
<div class="line">{</div>
<div class="line">  <span class="keywordflow">return</span> <a class="code" href="group__intrinsic___s_i_m_d__gr.html#ga4262f73be75efbac6b46ab7c71aa6cbc" title="GE setting dual 16-bit signed subtraction.">__SSUB16</a>(val1, val2);</div>
<div class="line">}</div>
</div><!-- fragment --><p><b>Multiplication:</b> Performing a multiplication using SIMD function</p>
<div class="fragment"><div class="line">uint32_t dual_mul_add_products(uint32_t val1, uint32_t val2)</div>
<div class="line">{</div>
<div class="line">  <span class="keywordflow">return</span> <a class="code" href="group__intrinsic___s_i_m_d__gr.html#gae326e368a1624d2dfb4b97c626939257" title="Q setting sum of dual 16-bit signed multiply.">__SMUAD</a>(val1, val2);</div>
<div class="line">}</div>
</div><!-- fragment --> <h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaefb8ebf3a54e197464da1ff69a44f4b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __PKHBT </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Combine a halfword from one register with a halfword from another register. The second argument can be left-shifted before extraction of the halfword. The registers PC and SP are not allowed as arguments. This instruction does not change the flags.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands </td></tr>
    <tr><td class="paramname">val3</td><td>value for left-shifting <em>val2</em>. Value range [0..31].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the combination of halfwords.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]</div>
<div class="line">res[31:16] = val2[31:16]&lt;&lt;val3 </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gafd8fe4a6d87e947caa81a69ec36c1666"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __PKHTB </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Combines a halfword from one register with a halfword from another register. The second argument can be right-shifted before extraction of the halfword. The registers PC and SP are not allowed as arguments. This instruction does not change the flags.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>second 16-bit operands </td></tr>
    <tr><td class="paramname">val2</td><td>first 16-bit operands </td></tr>
    <tr><td class="paramname">val3</td><td>value for right-shifting <em>val2</em>. Value range [1..32].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the combination of halfwords.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val2[15:0]&gt;&gt;val3</div>
<div class="line">res[31:16] = val1[31:16] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga17b873f246c9f5e9355760ffef3dad4a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QADD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to obtain the saturating add of two integers.<br/>
 The Q bit is set if the operation saturates.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first summand of the saturating add operation. </td></tr>
    <tr><td class="paramname">val2</td><td>second summand of the saturating add operation.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the saturating addition of val1 and val2.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[31:0] = SAT(val1 + SAT(val2))</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gae83a53ec04b496304bed6d9fe8f7461b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit integer arithmetic additions in parallel, saturating the results to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the saturated addition of the low halfwords, in the low halfword of the return value. </li>
<li>the saturated addition of the high halfwords, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0]</div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf2f5a9132dcfc6d01d34cd971c425713"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four 8-bit integer additions, saturating the results to the 8-bit signed integer range -2<sup>7</sup> &lt;= x &lt;= 2<sup>7</sup> - 1. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the saturated addition of the first byte of each operand in the first byte of the return value. </li>
<li>the saturated addition of the second byte of each operand in the second byte of the return value. </li>
<li>the saturated addition of the third byte of each operand in the third byte of the return value. </li>
<li>the saturated addition of the fourth byte of each operand in the fourth byte of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 16-bit signed integer range -2<sup>7</sup> &lt;= x &lt;= 2<sup>7</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8] </div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16] </div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24]          </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga87618799672e1511e33964bc71467eb3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the one operand, then add the high halfwords and subtract the low halfwords, saturating the results to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the subtraction in the low halfword, and the first operand for the addition in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the subtraction in the high halfword, and the second operand for the addition in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the saturated subtraction of the high halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the saturated addition of the high halfword in the first operand and the low halfword in the second operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[31:16]   </div>
<div class="line">res[31:16] = val1[31:16] + val2[15:0]  </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gab41eb2b17512ab01d476fc9d5bd19520"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QSAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of one operand, then subtract the high halfwords and add the low halfwords, saturating the results to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the addition in the low halfword, and the first operand for the subtraction in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the addition in the high halfword, and the second operand for the subtraction in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the saturated addition of the low halfword of the first operand and the high halfword of the second operand, in the low halfword of the return value. </li>
<li>the saturated subtraction of the low halfword of the second operand from the high halfword of the first operand, in the high halfword of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] - val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3ba259f8f05a36f7b88b469a71ffc096"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QSUB </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to obtain the saturating subtraction of two integers.<br/>
 The Q bit is set if the operation saturates.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>minuend of the saturating subtraction operation. </td></tr>
    <tr><td class="paramname">val2</td><td>subtrahend of the saturating subtraction operation.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the saturating subtraction of val1 and val2.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[31:0] = SAT(val1 - SAT(val2))</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad089605c16df9823a2c8aaa37777aae5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QSUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit integer subtractions, saturating the results to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the saturated subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the returned result. </li>
<li>the saturated subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the returned result.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 16-bit signed integer range -2<sup>15</sup> &lt;= x &lt;= 2<sup>15</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]</div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga753493a65493880c28baa82c151a0d61"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __QSUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four 8-bit integer subtractions, saturating the results to the 8-bit signed integer range -2<sup>7</sup> &lt;= x &lt;= 2<sup>7</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The returned results are saturated to the 8-bit signed integer range -2<sup>7</sup> &lt;= x &lt;= 2<sup>7</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad0bf46373a1c05aabf64517e84be5984"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed integer additions.<br/>
 The GE bits in the APSR are set according to the results of the additions.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfwords in the low halfword of the return value. </li>
<li>the addition of the high halfwords in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0]</div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gac20aa0f741d0a1494d58c531e38d5785"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function performs four 8-bit signed integer additions. The GE bits of the APSR are set according to the results of the additions. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the first bytes from each operand, in the first byte of the return value. </li>
<li>the addition of the second bytes of each operand, in the second byte of the return value. </li>
<li>the addition of the third bytes of each operand, in the third byte of the return value. </li>
<li>the addition of the fourth bytes of each operand, in the fourth byte of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[7:0] &gt;= 0 then APSR.GE[0] = 1 else 0 </li>
<li>if res[15:8] &gt;= 0 then APSR.GE[1] = 1 else 0 </li>
<li>if res[23:16] &gt;= 0 then APSR.GE[2] = 1 else 0 </li>
<li>if res[31:24] &gt;= 0 then APSR.GE[3] = 1 else 0</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8] </div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16] </div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24]          </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5845084fd99c872e98cf5553d554de2a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function inserts an SASX instruction into the instruction stream generated by the compiler. It enables you to exchange the halfwords of the second operand, add the high halfwords and subtract the low halfwords.<br/>
 The GE bits in the APRS are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the subtraction in the low halfword, and the first operand for the addition in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the subtraction in the high halfword, and the second operand for the addition in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the high halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the addition of the high halfword in the first operand and the low halfword in the second operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[31:16]   </div>
<div class="line">res[31:16] = val1[31:16] + val2[15:0]  </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf5448e591fe49161b6759b48aecb08fe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SEL </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function inserts a SEL instruction into the instruction stream generated by the compiler. It enables you to select bytes from the input parameters, whereby the bytes that are selected depend upon the results of previous SIMD instruction function. The results of previous SIMD instruction function are represented by the Greater than or Equal flags in the Application Program Status Register (APSR). The __SEL function works equally well on both halfword and byte operand function results. This is because halfword operand operations set two (duplicate) GE bits per value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>four selectable 8-bit values. </td></tr>
    <tr><td class="paramname">val2</td><td>four selectable 8-bit values.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function selects bytes from the input parameters and returns them in the return value, res, according to the following criteria: <ul>
<li>if APSR.GE[0] == 1 then res[7:0] = val1[7:0] else res[7:0] = val2[7:0] </li>
<li>if APSR.GE[1] == 1 then res[15:8] = val1[15:8] else res[15:8] = val2[15:8] </li>
<li>if APSR.GE[2] == 1 then res[23:16] = val1[23:16] else res[23:16] = val2[23:16] </li>
<li>if APSR.GE[3] == 1 then res[31;24] = val1[31:24] else res = val2[31:24] </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga15d8899a173effb8ad8c7268da32b60e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two signed 16-bit integer additions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the low halfwords, in the low halfword of the return value. </li>
<li>the halved addition of the high halfwords, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0]  &gt;&gt; 1</div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16] &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga524575b442ea01aec10c762bf4d85fea"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four signed 8-bit integer additions, halving the results. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the first bytes from each operand, in the first byte of the return value. </li>
<li>the halved addition of the second bytes from each operand, in the second byte of the return value. </li>
<li>the halved addition of the third bytes from each operand, in the third byte of the return value. </li>
<li>the halved addition of the fourth bytes from each operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0]  &gt;&gt; 1</div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8] &gt;&gt; 1</div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16] &gt;&gt; 1</div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24] &gt;&gt; 1         </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gae0a649035f67627464fd80e7218c89d5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the two halfwords of one operand, perform one signed 16-bit integer addition and one signed 16-bit subtraction, and halve the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the high halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the halved subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = (val1[15:0]  - val2[31:16]) &gt;&gt; 1  </div>
<div class="line">res[31:16] = (val1[31:16] - val2[15:0] ) &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gafadbd89c36b5addcf1ca10dd392db3e9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHSAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the two halfwords of one operand, perform one signed 16-bit integer subtraction and one signed 16-bit addition, and halve the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the low halfword in the first operand and the high halfword in the second operand, in the low halfword of the return value. </li>
<li>the halved subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = (val1[15:0]  + val2[31:16]) &gt;&gt; 1</div>
<div class="line">res[31:16] = (val1[31:16] - val2[15:0] ) &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga31328467f0f91b8ff9ae9a01682ad3bf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHSUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two signed 16-bit integer subtractions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the returned result. </li>
<li>the halved subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the returned result.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]   &gt;&gt; 1</div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]  &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gac3ec7215b354d925a239f3b31df2b77b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SHSUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four signed 8-bit integer subtractions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the halved subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the halved subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the halved subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0]   &gt;&gt; 1</div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]  &gt;&gt; 1</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16] &gt;&gt; 1</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24] &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gae0c86f3298532183f3a29f5bb454d354"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMLAD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two signed 16-bit multiplications, adding both results to a 32-bit accumulate operand.<br/>
 The Q bit is set if the addition overflows. Overflow cannot occur during the multiplications.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the product of each multiplication added to the accumulate value, as a 32-bit integer.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">res[31:0] = p1 + p2 + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9c286d330f4fb29b256335add91eec9f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMLADX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two signed 16-bit multiplications with exchanged halfwords of the second operand, adding both results to a 32-bit accumulate operand.<br/>
 The Q bit is set if the addition overflows. Overflow cannot occur during the multiplications.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the product of each multiplication with exchanged halfwords of the second operand added to the accumulate value, as a 32-bit integer.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">res[31:0] = p1 + p2 + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad80e9b20c1736fd798f897362273a146"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t __SMLALD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two signed 16-bit multiplications, adding both results to a 64-bit accumulate operand. Overflow is only possible as a result of the 64-bit addition. This overflow is not detected if it occurs. Instead, the result wraps around modulo2<sup>64</sup>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the product of each multiplication added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">sum = p1 + p2 + val3[63:32][31:0]</div>
<div class="line">res[63:32] = sum[63:32]</div>
<div class="line">res[31:0]  = sum[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad1adad1b3f2667328cc0db6c6b4f41cf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned long long __SMLALDX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long long&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand, and perform two signed 16-bit multiplications, adding both results to a 64-bit accumulate operand. Overflow is only possible as a result of the 64-bit addition. This overflow is not detected if it occurs. Instead, the result wraps around modulo2<sup>64</sup>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the product of each multiplication added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">sum = p1 + p2 + val3[63:32][31:0]</div>
<div class="line">res[63:32] = sum[63:32]</div>
<div class="line">res[31:0] = sum[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf4350af7f2030c36f43b2c104a9d16cd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMLSD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed multiplications, take the difference of the products, subtracting the high halfword product from the low halfword product, and add the difference to a 32-bit accumulate operand.<br/>
 The Q bit is set if the accumulation overflows. Overflow cannot occur during the multiplications or the subtraction.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the product of each multiplication, added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">res[31:0] = p1 - p2 + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5290ce5564770ad124910d2583dc0a9e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMLSDX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords in the second operand, then perform two 16-bit signed multiplications. The difference of the products is added to a 32-bit accumulate operand.<br/>
 The Q bit is set if the addition overflows. Overflow cannot occur during the multiplications or the subtraction.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the product of each multiplication, added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">res[31:0] = p1 - p2 + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5611f7314e0c8f53da377918dfbf42ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t __SMLSLD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function It enables you to perform two 16-bit signed multiplications, take the difference of the products, subtracting the high halfword product from the low halfword product, and add the difference to a 64-bit accumulate operand. Overflow cannot occur during the multiplications or the subtraction. Overflow can occur as a result of the 64-bit addition, and this overflow is not detected. Instead, the result wraps round to modulo2<sup>64</sup>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the product of each multiplication, added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">res[63:0] = p1 - p2 + val3[63:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga83e69ef81057d3cbd06863d729385187"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned long long __SMLSLDX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long long&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand, perform two 16-bit multiplications, adding the difference of the products to a 64-bit accumulate operand. Overflow cannot occur during the multiplications or the subtraction. Overflow can occur as a result of the 64-bit addition, and this overflow is not detected. Instead, the result wraps round to modulo2<sup>64</sup>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the product of each multiplication, added to the accumulate value.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">res[63:0] = p1 - p2 + val3[63:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaea60757232f740ec6b09980eebb614ff"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMMLA </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform a signed 32-bit multiplications, adding the most significant 32 bits of the 64-bit result to a 32-bit accumulate operand.<br/>
</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for multiplication. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulate value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the product of multiplication (most significant 32 bits) is added to the accumulate value, as a 32-bit integer.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p = val1 * val2</div>
<div class="line">res[31:0] = p[61:32] + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gae326e368a1624d2dfb4b97c626939257"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMUAD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed multiplications, adding the products together.<br/>
 The Q bit is set if the addition overflows.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the sum of the products of the two 16-bit signed multiplications.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">res[31:0] = p1 + p2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaee6390f86965cb662500f690b0012092"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMUADX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed multiplications with exchanged halfwords of the second operand, adding the products together.<br/>
 The Q bit is set if the addition overflows.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the sum of the products of the two 16-bit signed multiplications with exchanged halfwords of the second operand.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">res[31:0] = p1 + p2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga039142a5368840683cf329cb55b73f84"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMUSD </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed multiplications, taking the difference of the products by subtracting the high halfword product from the low halfword product.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the products of the two 16-bit signed multiplications.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[15:0]</div>
<div class="line">p2 = val1[31:16] * val2[31:16]</div>
<div class="line">res[31:0] = p1 - p2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gabb5bcba694bf17b141c32e6a8474f60e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SMUSDX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed multiplications, subtracting one of the products from the other. The halfwords of the second operand are exchanged before performing the arithmetic. This produces top * bottom and bottom * top multiplication.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operands for each multiplication. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit operands for each multiplication.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the difference of the products of the two 16-bit signed multiplications.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">p1 = val1[15:0]  * val2[31:16]</div>
<div class="line">p2 = val1[31:16] * val2[15:0]</div>
<div class="line">res[31:0] = p1 - p2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga95e666b82216066bf6064d1244e6883c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SSAT16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to saturate two signed 16-bit values to a selected signed range.<br/>
 The Q bit is set if either operation saturates.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>two signed 16-bit values to be saturated. </td></tr>
    <tr><td class="paramname">val2</td><td>bit position for saturation, an integral constant expression in the range 1 to 16.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the sum of the absolute differences of the following bytes, added to the accumulation value: <ul>
<li>the signed saturation of the low halfword in <em>val1</em>, saturated to the bit position specified in <em>val2</em> and returned in the low halfword of the return value. </li>
<li>the signed saturation of the high halfword in <em>val1</em>, saturated to the bit position specified in <em>val2</em> and returned in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">Saturate halfwords in val1 to the <span class="keywordtype">signed</span> range specified by the bit position in val2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9d3bc5c539f9bd50f7d59ffa37ac6a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SSAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the two halfwords of one operand and perform one 16-bit integer subtraction and one 16-bit addition.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the addition in the low halfword, and the first operand for the subtraction in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the addition in the high halfword, and the second operand for the subtraction in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfword in the first operand and the high halfword in the second operand, in the low halfword of the return value. </li>
<li>the subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] - val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4262f73be75efbac6b46ab7c71aa6cbc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SSUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit signed integer subtractions.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands of each subtraction. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands of each subtraction.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <ul>
<li>res is the return value, then: </li>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]</div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaba63bb52e1e93fb527e26f3d474da12e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SSUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four 8-bit signed integer subtractions.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands of each subtraction. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands of each subtraction.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on </dt><dd>the results of the operation.</dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[8:0] &gt;= 0 then APSR.GE[0] = 1 else 0 </li>
<li>if res[15:8] &gt;= 0 then APSR.GE[1] = 1 else 0 </li>
<li>if res[23:16] &gt;= 0 then APSR.GE[2] = 1 else 0 </li>
<li>if res[31:24] &gt;= 0 then APSR.GE[3] = 1 else 0</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gac540b4fc41d30778ba102d2a65db5589"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SXTAB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to extract two 8-bit values from the second operand (at bit positions [7:0] and [23:16]), sign-extend them to 16-bits each, and add the results to the first operand.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>values added to the zero-extended to 16-bit values. </td></tr>
    <tr><td class="paramname">val2</td><td>two 8-bit values to be extracted and zero-extended.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the addition of <em>val1</em> and <em>val2</em>, where the 8-bit values in val2[7:0] and val2[23:16] have been extracted and sign-extended prior to the addition.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + SignExtended(val2[7:0])</div>
<div class="line">res[31:16] = val1[31:16] + SignExtended(val2[23:16])</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga38dce3dd13ba212e80ec3cff4abeb11a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SXTB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to extract two 8-bit values from an operand and sign-extend them to 16 bits each.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val</td><td>two 8-bit values in val[7:0] and val[23:16] to be sign-extended.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the 8-bit values sign-extended to 16-bit values. <ul>
<li>sign-extended value of val[7:0] in the low halfword of the return value. </li>
<li>sign-extended value of val[23:16] in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = SignExtended(val[7:0]</div>
<div class="line">res[31:16] = SignExtended(val[23:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa1160f0cf76d6aa292fbad54a1aa6b74"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit unsigned integer additions.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands for each addition. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands for each addition.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfwords in each operand, in the low halfword of the return value. </li>
<li>the addition of the high halfwords in each operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0x10000 then APSR.GE[0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0x10000 then APSR.GE[1] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0] </div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gab3d7fd00d113b20fb3741a17394da762"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit integer additions. The GE bits of the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands for each addition. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands for each addition.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the first bytes from each operand, in the first byte of the return value. </li>
<li>the halved addition of the second bytes from each operand, in the second byte of the return value. </li>
<li>the halved addition of the third bytes from each operand, in the third byte of the return value. </li>
<li>the halved addition of the fourth bytes from each operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation.</dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[7:0] &gt;= 0x100 then APSR.GE[0] = 1 else 0 </li>
<li>if res[15:8] &gt;= 0x100 then APSR.GE[1] = 1 else 0 </li>
<li>if res[23:16] &gt;= 0x100 then APSR.GE[2] = 1 else 0 </li>
<li>if res[31:24] &gt;= 0x100 then APSR.GE[3] = 1 else 0</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga980353d2c72ebb879282e49f592fddc0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the two halfwords of the second operand, add the high halfwords and subtract the low halfwords.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the subtraction in the low halfword, and the first operand for the addition in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the subtraction in the high halfword and the second operand for the addition in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the high halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the addition of the high halfword in the first operand and the low halfword in the second operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation.</dd></dl>
<dl class="section user"><dt>If <em>res</em> is the return value, then:</dt><dd><ul>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0x10000 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] + val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gabd0b0e2da2e6364e176d051687702b86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two unsigned 16-bit integer additions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the low halfwords in each operand, in the low halfword of the return value. </li>
<li>the halved addition of the high halfwords in each operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0]   &gt;&gt; 1</div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16]  &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3a14e5485e59bf0f23595b7c2a94eb0b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit integer additions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the first bytes in each operand, in the first byte of the return value. </li>
<li>the halved addition of the second bytes in each operand, in the second byte of the return value. </li>
<li>the halved addition of the third bytes in each operand, in the third byte of the return value. </li>
<li>the halved addition of the fourth bytes in each operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0]   &gt;&gt; 1</div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8]  &gt;&gt; 1</div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16] &gt;&gt; 1</div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24] &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga028f0732b961fb6e5209326fb3855261"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand, add the high halfwords and subtract the low halfwords, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the subtraction in the low halfword, and the first operand for the addition in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the subtraction in the high halfword, and the second operand for the addition in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the high halfword in the second operand from the low halfword in the first operand. </li>
<li>the halved addition of the high halfword in the first operand and the low halfword in the second operand.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = (val1[15:0]  - val2[31:16]) &gt;&gt; 1</div>
<div class="line">res[31:16] = (val1[31:16] + val2[15:0] ) &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga09e129e6613329aab87c89f1108b7ed7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHSAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand, subtract the high halfwords and add the low halfwords, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the addition in the low halfword, and the first operand for the subtraction in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the addition in the high halfword, and the second operand for the subtraction in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the high halfword in the second operand and the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the halved subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = (val1[15:0]  + val2[31:16]) &gt;&gt; 1</div>
<div class="line">res[31:16] = (val1[31:16] - val2[15:0] ) &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1f7545b8dc33bb97982731cb9d427a69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHSUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two unsigned 16-bit integer subtractions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the halved subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]   &gt;&gt; 1</div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]  &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga48a55df1c3e73923b73819d7c19b392d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UHSUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit integer subtractions, halving the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the halved subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the halved subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the halved subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0]    &gt;&gt; 1</div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]   &gt;&gt; 1</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16]  &gt;&gt; 1</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24]  &gt;&gt; 1</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9e2cc5117e79578a08b25f1e89022966"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQADD16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two unsigned 16-bit integer additions, saturating the results to the 16-bit unsigned integer range 0 &lt; x &lt; 2<sup>16</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfword in the first operand and the low halfword in the second operand, in the low halfword of the return value. </li>
<li>the addition of the high halfword in the first operand and the high halfword in the second operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 16-bit unsigned integer range 0 &lt; x &lt; 2<sup>16</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[15:0] </div>
<div class="line">res[31:16] = val1[31:16] + val2[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gafa9af218db3934a692fb06fa728d8031"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQADD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit integer additions, saturating the results to the 8-bit unsigned integer range 0 &lt; x &lt; 2<sup>8</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit summands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit summands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the halved addition of the first bytes in each operand, in the first byte of the return value. </li>
<li>the halved addition of the second bytes in each operand, in the second byte of the return value. </li>
<li>the halved addition of the third bytes in each operand, in the third byte of the return value. </li>
<li>the halved addition of the fourth bytes in each operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 8-bit unsigned integer range 0 &lt; x &lt; 2<sup>8</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   + val2[7:0] </div>
<div class="line">res[15:8]  = val1[15:8]  + val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] + val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] + val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5eff3ae5eabcd73f3049996ca391becb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQASX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand and perform one unsigned 16-bit integer addition and one unsigned 16-bit subtraction, saturating the results to the 16-bit unsigned integer range 0 &lt;= x &lt;= 2<sup>16</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the high halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 16-bit unsigned integer range 0 &lt;= x &lt;= 2<sup>16</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] + val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gadecfdfabc328d8939d49d996f2fd4482"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQSAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand and perform one unsigned 16-bit integer subtraction and one unsigned 16-bit addition, saturating the results to the 16-bit unsigned integer range 0 &lt;= x &lt;= 2<sup>16</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first 16-bit operand for the addition in the low halfword, and the first 16-bit operand for the subtraction in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second 16-bit halfword for the addition in the high halfword, and the second 16-bit halfword for the subtraction in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfword in the first operand and the high halfword in the second operand, in the low halfword of the return value. </li>
<li>the subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 16-bit unsigned integer range 0 &lt;= x &lt;= 2<sup>16</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] - val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5ec4e2e231d15e5c692233feb3806187"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQSUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two unsigned 16-bit integer subtractions, saturating the results to the 16-bit unsigned integer range 0 &lt; x &lt; 2<sup>16</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands for each subtraction. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands for each subtraction.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 16-bit unsigned integer range 0 &lt; x &lt; 2<sup>16</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]   </div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]  </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9736fe816aec74fe886e7fb949734eab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UQSUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit integer subtractions, saturating the results to the 8-bit unsigned integer range 0 &lt; x &lt; 2<sup>8</sup> - 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The results are saturated to the 8-bit unsigned integer range 0 &lt; x &lt; 2<sup>8</sup> - 1.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0]</div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gac8855c07044239ea775c8128013204f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USAD8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit subtractions, and add the absolute values of the differences together, returning the result as a single unsigned integer.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands for the subtractions. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands for the subtractions.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>The sum is returned as a single unsigned integer.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">absdiff1  = val1[7:0]   - val2[7:0]</div>
<div class="line">absdiff2  = val1[15:8]  - val2[15:8]</div>
<div class="line">absdiff3  = val1[23:16] - val2[23:16]</div>
<div class="line">absdiff4  = val1[31:24] - val2[31:24]</div>
<div class="line">res[31:0] = absdiff1 + absdiff2 + absdiff3 + absdiff4</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad032bd21f013c5d29f5fcb6b0f02bc3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USADA8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four unsigned 8-bit subtractions, and add the absolute values of the differences to a 32-bit accumulate operand.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands for the subtractions. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands for the subtractions. </td></tr>
    <tr><td class="paramname">val3</td><td>accumulation value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the sum of the absolute differences of the following bytes, added to the accumulation value: <ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">absdiff1  = val1[7:0]   - val2[7:0]</div>
<div class="line">absdiff2  = val1[15:8]  - val2[15:8]</div>
<div class="line">absdiff3  = val1[23:16] - val2[23:16]</div>
<div class="line">absdiff4  = val1[31:24] - val2[31:24]</div>
<div class="line">sum       = absdiff1 + absdiff2 + absdiff3 + absdiff4</div>
<div class="line">res[31:0] = sum[31:0] + val3[31:0]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga967f516afff5900cf30f1a81907cdd89"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USAT16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to saturate two signed 16-bit values to a selected unsigned range.<br/>
 The Q bit is set if either operation saturates.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>two 16-bit values that are to be saturated. </td></tr>
    <tr><td class="paramname">val2</td><td>bit position for saturation, and must be an integral constant expression in the range 0 to 15.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the saturation of the two signed 16-bit values, as non-negative values. <ul>
<li>the saturation of the low halfword in <em>val1</em>, saturated to the bit position specified in <em>val2</em> and returned in the low halfword of the return value. </li>
<li>the saturation of the high halfword in <em>val1</em>, saturated to the bit position specified in <em>val2</em> and returned in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">Saturate halfwords in val1 to the <span class="keywordtype">unsigned</span> range specified by the bit position in val2</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga578a082747436772c482c96d7a58e45e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USAX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to exchange the halfwords of the second operand, subtract the high halfwords and add the low halfwords.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first operand for the addition in the low halfword, and the first operand for the subtraction in the high halfword. </td></tr>
    <tr><td class="paramname">val2</td><td>second operand for the addition in the high halfword, and the second operand for the subtraction in the low halfword.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the addition of the low halfword in the first operand and the high halfword in the second operand, in the low halfword of the return value. </li>
<li>the subtraction of the low halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value. </li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation. </dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0x10000 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  + val2[31:16]</div>
<div class="line">res[31:16] = val1[31:16] - val2[15:0] </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9f2b77e11fc4a77b26c36c423ed45b4e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USUB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform two 16-bit unsigned integer subtractions.<br/>
 The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first two 16-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second two 16-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the low halfword in the second operand from the low halfword in the first operand, in the low halfword of the return value. </li>
<li>the subtraction of the high halfword in the second operand from the high halfword in the first operand, in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation.</dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[15:0] &gt;= 0 then APSR.GE[1:0] = 11 else 00 </li>
<li>if res[31:16] &gt;= 0 then APSR.GE[3:2] = 11 else 00</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = val1[15:0]  - val2[15:0]   </div>
<div class="line">res[31:16] = val1[31:16] - val2[31:16]  </div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gacb7257dc3b8e9acbd0ef0e31ff87d4b8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USUB8 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to perform four 8-bit unsigned integer subtractions. The GE bits in the APSR are set according to the results.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>first four 8-bit operands. </td></tr>
    <tr><td class="paramname">val2</td><td>second four 8-bit operands.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>the subtraction of the first byte in the second operand from the first byte in the first operand, in the first bytes of the return value. </li>
<li>the subtraction of the second byte in the second operand from the second byte in the first operand, in the second byte of the return value. </li>
<li>the subtraction of the third byte in the second operand from the third byte in the first operand, in the third byte of the return value. </li>
<li>the subtraction of the fourth byte in the second operand from the fourth byte in the first operand, in the fourth byte of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Each bit in APSR.GE is set or cleared for each byte in the return value, depending on the results of the operation.</dd></dl>
<dl class="section user"><dt></dt><dd>If <em>res</em> is the return value, then: <ul>
<li>if res[8:0] &gt;= 0 then APSR.GE[0] = 1 else 0 </li>
<li>if res[15:8] &gt;= 0 then APSR.GE[1] = 1 else 0 </li>
<li>if res[23:16] &gt;= 0 then APSR.GE[2] = 1 else 0 </li>
<li>if res[31:24] &gt;= 0 then APSR.GE[3] = 1 else 0</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[7:0]   = val1[7:0]   - val2[7:0]</div>
<div class="line">res[15:8]  = val1[15:8]  - val2[15:8]</div>
<div class="line">res[23:16] = val1[23:16] - val2[23:16]</div>
<div class="line">res[31:24] = val1[31:24] - val2[31:24]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gad25ce96db0f17096bbd815f4817faf09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UXTAB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to extract two 8-bit values from one operand, zero-extend them to 16 bits each, and add the results to two 16-bit values from another operand.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val1</td><td>value added to the zero-extended to 16-bit values. </td></tr>
    <tr><td class="paramname">val2</td><td>two 8-bit values to be extracted and zero-extended.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the 8-bit values in <em>val2</em>, zero-extended to 16-bit values and added to <em>val1</em>.</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = ZeroExt(val2[7:0]   to 16 bits) + val1[15:0]</div>
<div class="line">res[31:16] = ZeroExt(val2[31:16] to 16 bits) + val1[31:16]</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
<a class="anchor" id="gab41d713653b16f8d9fef44d14e397228"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __UXTB16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>val</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables you to extract two 8-bit values from an operand and zero-extend them to 16 bits each.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">val</td><td>two 8-bit values in val[7:0] and val[23:16] to be sign-extended.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the 8-bit values zero-extended to 16-bit values. <ul>
<li>zero-extended value of val[7:0] in the low halfword of the return value. </li>
<li>zero-extended value of val[23:16] in the high halfword of the return value.</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Operation:</dt><dd><div class="fragment"><div class="line">res[15:0]  = ZeroExtended(val[7:0]  )</div>
<div class="line">res[31:16] = ZeroExtended(val[23:16])</div>
</div><!-- fragment --> </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
