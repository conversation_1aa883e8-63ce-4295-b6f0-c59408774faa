/**
  ******************************************************************************
  * @file    main_hmi_integration.c
  * <AUTHOR>
  * @version V1.0
  * @date    2025-01-01
  * @brief   串口屏集成主程序示例 - 电赛G题完整方案
  *          集成双模块信号发生器 + 串口触摸屏人机界面
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// 信号生成模块
#include "../Modules/Generation/signal_generator.h"

// 人机界面模块
#include "../Modules/Interface/hmi_screen.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void System_Init(void);
void HMI_EventHandler(HMI_EventData_t* event);
void System_MainLoop(void);

/**
  * @brief  主函数 - 串口屏集成方案
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    System_Init();
    
    /* 主循环 */
    System_MainLoop();
}

/**
  * @brief  系统初始化
  * @param  None
  * @retval None
  */
void System_Init(void)
{
    /* 1. 基础系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();
    
    /* 2. 信号发生器初始化 */
    if (SigGen_Init() != 0) {
        // 初始化失败处理
        while (1) {
            // 错误指示
        }
    }
    
    /* 3. HMI串口屏初始化 */
    if (HMI_Init() != 0) {
        // 初始化失败处理
        while (1) {
            // 错误指示
        }
    }
    
    /* 4. 设置初始参数 */
    SigGen_SetFrequency(1000);      // 1kHz
    SigGen_SetAmplitude(2.5f);      // 2.5V
    SigGen_SetWaveType(SIGGEN_WAVE_SINE);  // 正弦波
    SigGen_SetOutputEnable(true);   // 使能输出
    
    /* 5. 更新HMI显示 */
    SignalGen_Params_t params;
    SigGen_Config_t config;
    SigGen_GetConfig(&config);
    
    params.frequency = config.frequency;
    params.amplitude = config.amplitude;
    params.wave_type = config.wave_type;
    params.active_module = SigGen_GetActiveModule();
    params.output_enable = config.output_enable;
    
    HMI_UpdateSignalParams(&params);
    HMI_UpdateStatus("System Ready");
}

/**
  * @brief  系统主循环
  * @param  None
  * @retval None
  */
void System_MainLoop(void)
{
    uint32_t last_display_update = 0;
    
    while (1)
    {
        uint32_t current_time = SysTick_GetTick();
        
        /* 1. 处理HMI事件 */
        HMI_EventData_t* event = HMI_ProcessEvents();
        if (event != NULL) {
            HMI_EventHandler(event);
        }
        
        /* 2. 信号发生器主循环处理 */
        SigGen_MainLoop();
        
        /* 3. HMI主循环处理 */
        HMI_MainLoop();
        
        /* 4. 定期更新显示 (每500ms) */
        if (current_time - last_display_update >= 500) {
            last_display_update = current_time;
            
            // 更新实时状态显示
            SigGen_Config_t config;
            SigGen_GetConfig(&config);
            
            SignalGen_Params_t params;
            params.frequency = config.frequency;
            params.amplitude = config.amplitude;
            params.wave_type = config.wave_type;
            params.active_module = SigGen_GetActiveModule();
            params.output_enable = config.output_enable;
            
            HMI_UpdateSignalParams(&params);
            
            // 更新状态信息
            char status_text[64];
            sprintf(status_text, "%s Active - %s", 
                    SigGen_GetModuleName(SigGen_GetActiveModule()),
                    SigGen_GetWaveName(config.wave_type));
            HMI_UpdateStatus(status_text);
        }
        
        /* 5. 短暂延时 */
        Delay_ms(1);
    }
}

/**
  * @brief  HMI事件处理函数
  * @param  event: 事件数据指针
  * @retval None
  */
void HMI_EventHandler(HMI_EventData_t* event)
{
    if (event == NULL) {
        return;
    }
    
    switch (event->event_type) {
        case HMI_EVENT_FREQ_SET:
            // 频率设置事件
            if (SigGen_SetFrequency(event->param1) == 0) {
                HMI_UpdateStatus("Frequency Updated");
            } else {
                HMI_UpdateStatus("Frequency Error");
            }
            break;
            
        case HMI_EVENT_AMP_SET:
            // 幅度设置事件
            if (SigGen_SetAmplitude(event->param_float) == 0) {
                HMI_UpdateStatus("Amplitude Updated");
            } else {
                HMI_UpdateStatus("Amplitude Error");
            }
            break;
            
        case HMI_EVENT_WAVE_SET:
            // 波形设置事件
            if (SigGen_SetWaveType((SigGen_WaveType_t)event->param1) == 0) {
                HMI_UpdateStatus("Waveform Updated");
            } else {
                HMI_UpdateStatus("Waveform Error");
            }
            break;
            
        case HMI_EVENT_OUTPUT_TOGGLE:
            // 输出开关事件
            {
                SigGen_Config_t config;
                SigGen_GetConfig(&config);
                bool new_state = !config.output_enable;
                
                if (SigGen_SetOutputEnable(new_state) == 0) {
                    HMI_UpdateStatus(new_state ? "Output ON" : "Output OFF");
                } else {
                    HMI_UpdateStatus("Output Toggle Error");
                }
            }
            break;
            
        case HMI_EVENT_RESET:
            // 复位事件
            SigGen_SetFrequency(SIGGEN_DEFAULT_FREQ);
            SigGen_SetAmplitude(SIGGEN_DEFAULT_AMP);
            SigGen_SetWaveType(SIGGEN_DEFAULT_WAVE);
            SigGen_SetOutputEnable(false);
            HMI_UpdateStatus("System Reset");
            break;
            
        case HMI_EVENT_SWEEP_START:
            // 开始扫频事件
            if (SigGen_FrequencySweep(100, 10000, 100, 100) == 0) {
                HMI_UpdateStatus("Sweep Started");
            } else {
                HMI_UpdateStatus("Sweep Error");
            }
            break;
            
        case HMI_EVENT_SWEEP_STOP:
            // 停止扫频事件
            SigGen_StopSweep();
            HMI_UpdateStatus("Sweep Stopped");
            break;
            
        default:
            break;
    }
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    // 配置系统时钟为168MHz
    // 这里使用标准的STM32F4时钟配置
    // 具体实现根据实际需求调整
}

/**
  * @brief  SysTick中断处理函数
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
    uwTick++;
}

/************************ (C) COPYRIGHT STM32F4电赛项目组 *****END OF FILE****/
