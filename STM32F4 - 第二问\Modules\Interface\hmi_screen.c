/**
  ******************************************************************************
  * @file    hmi_screen.c
  * <AUTHOR>
  * @version V1.0
  * @date    2025-01-01
  * @brief   串口触摸屏驱动模块实现 - 电赛G题专用
  ******************************************************************************
  */

#include "hmi_screen.h"
#include "../Core/systick.h"
#include <string.h>
#include <stdarg.h>

/* Private variables ---------------------------------------------------------*/
static USART_TypeDef* s_hmi_usart = USART2;
static char s_tx_buffer[HMI_TX_BUFFER_SIZE];
static volatile bool s_tx_complete = true;
static HMI_EventData_t s_current_event;

/* Global variables ----------------------------------------------------------*/
HMI_Status_t g_hmi_status = {0};
SignalGen_Params_t g_signal_params = {
    .frequency = 1000,
    .amplitude = 2.5f,
    .wave_type = HMI_WAVE_SINE,
    .active_module = HMI_MODULE_DAC8552,
    .output_enable = false
};

/* Private function prototypes -----------------------------------------------*/
static void HMI_USART2_Init(void);
static void HMI_GPIO_Init(void);
static void HMI_NVIC_Init(void);
static int8_t HMI_SendCommand(const char* format, ...);
static void HMI_ParseReceivedData(void);

/**
  * @brief  HMI串口屏初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_Init(void)
{
    // 1. GPIO初始化
    HMI_GPIO_Init();
    
    // 2. USART2初始化
    HMI_USART2_Init();
    
    // 3. NVIC中断初始化
    HMI_NVIC_Init();
    
    // 4. 初始化状态
    memset(&g_hmi_status, 0, sizeof(g_hmi_status));
    g_hmi_status.connected = true;
    g_hmi_status.last_update_time = SysTick_GetTick();
    
    // 5. 发送初始化命令
    Delay_ms(100);  // 等待串口屏启动
    HMI_SendCommand("page 0");  // 切换到主页面
    Delay_ms(50);
    
    // 6. 初始化界面显示
    HMI_UpdateSignalParams(&g_signal_params);
    HMI_UpdateStatus("System Ready");
    
    return 0;
}

/**
  * @brief  GPIO初始化
  * @param  None
  * @retval None
  */
static void HMI_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOA时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    
    // 配置PA2 (USART2_TX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置PA3 (USART2_RX)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置引脚复用功能
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_USART2);
}

/**
  * @brief  USART2初始化
  * @param  None
  * @retval None
  */
static void HMI_USART2_Init(void)
{
    USART_InitTypeDef USART_InitStructure;
    
    // 使能USART2时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    
    // 配置USART2
    USART_InitStructure.USART_BaudRate = HMI_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(s_hmi_usart, &USART_InitStructure);
    
    // 使能接收中断
    USART_ITConfig(s_hmi_usart, USART_IT_RXNE, ENABLE);
    
    // 使能USART2
    USART_Cmd(s_hmi_usart, ENABLE);
}

/**
  * @brief  NVIC中断初始化
  * @param  None
  * @retval None
  */
static void HMI_NVIC_Init(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置USART2中断
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  发送命令到HMI屏幕
  * @param  format: 格式字符串
  * @param  ...: 可变参数
  * @retval 0: 成功, -1: 失败
  */
static int8_t HMI_SendCommand(const char* format, ...)
{
    va_list args;
    int length;
    
    if (!s_tx_complete) {
        return -1;  // 上次发送未完成
    }
    
    // 格式化命令字符串
    va_start(args, format);
    length = vsnprintf(s_tx_buffer, sizeof(s_tx_buffer) - 3, format, args);
    va_end(args);
    
    if (length <= 0) {
        return -1;
    }
    
    // 添加结束符
    s_tx_buffer[length] = 0xFF;
    s_tx_buffer[length + 1] = 0xFF;
    s_tx_buffer[length + 2] = 0xFF;
    length += 3;
    
    // 发送数据
    s_tx_complete = false;
    for (int i = 0; i < length; i++) {
        while (USART_GetFlagStatus(s_hmi_usart, USART_FLAG_TXE) == RESET);
        USART_SendData(s_hmi_usart, s_tx_buffer[i]);
    }
    
    // 等待发送完成
    while (USART_GetFlagStatus(s_hmi_usart, USART_FLAG_TC) == RESET);
    s_tx_complete = true;
    
    g_hmi_status.tx_count++;
    return 0;
}

/**
  * @brief  发送字符串到HMI屏幕
  * @param  component: 组件名称
  * @param  text: 文本内容
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendString(const char* component, const char* text)
{
    return HMI_SendCommand("%s.txt=\"%s\"", component, text);
}

/**
  * @brief  发送数值到HMI屏幕
  * @param  component: 组件名称
  * @param  value: 数值
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendNumber(const char* component, int32_t value)
{
    return HMI_SendCommand("%s.val=%d", component, value);
}

/**
  * @brief  发送浮点数到HMI屏幕
  * @param  component: 组件名称
  * @param  value: 浮点数值
  * @param  precision: 精度 (小数位数)
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_SendFloat(const char* component, float value, uint8_t precision)
{
    int32_t scaled_value = (int32_t)(value * 100);  // 保留2位小数
    return HMI_SendCommand("%s.val=%d", component, scaled_value);
}

/**
  * @brief  更新信号参数显示
  * @param  params: 信号参数结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_UpdateSignalParams(const SignalGen_Params_t* params)
{
    if (params == NULL) {
        return -1;
    }
    
    // 更新频率显示
    HMI_SendNumber("freq", params->frequency);
    
    // 更新幅度显示 (转换为整数，保留2位小数)
    HMI_SendFloat("amp", params->amplitude, 2);
    
    // 更新波形类型
    HMI_SendNumber("wave", params->wave_type);
    
    // 更新活动模块
    HMI_SendNumber("module", params->active_module);
    
    // 更新输出状态
    HMI_SendNumber("output", params->output_enable ? 1 : 0);
    
    return 0;
}

/**
  * @brief  更新系统状态显示
  * @param  status_text: 状态文本
  * @retval 0: 成功, -1: 失败
  */
int8_t HMI_UpdateStatus(const char* status_text)
{
    return HMI_SendString("status", status_text);
}

/**
  * @brief  解析接收到的数据
  * @param  None
  * @retval None
  */
static void HMI_ParseReceivedData(void)
{
    // 简化的命令解析示例
    if (g_hmi_status.rx_length > 0) {
        uint8_t cmd = g_hmi_status.rx_buffer[0];
        
        switch (cmd) {
            case '1':  // 频率设置命令
                s_current_event.event_type = HMI_EVENT_FREQ_SET;
                s_current_event.param1 = 1000;  // 示例值
                break;
                
            case '2':  // 幅度设置命令
                s_current_event.event_type = HMI_EVENT_AMP_SET;
                s_current_event.param_float = 2.5f;  // 示例值
                break;
                
            case '3':  // 波形设置命令
                s_current_event.event_type = HMI_EVENT_WAVE_SET;
                s_current_event.param1 = HMI_WAVE_SINE;
                break;
                
            default:
                s_current_event.event_type = HMI_EVENT_NONE;
                break;
        }
    }
}

/**
  * @brief  处理接收到的HMI事件
  * @param  None
  * @retval 事件数据指针，无事件时返回NULL
  */
HMI_EventData_t* HMI_ProcessEvents(void)
{
    if (g_hmi_status.rx_complete) {
        HMI_ParseReceivedData();
        g_hmi_status.rx_complete = false;
        g_hmi_status.rx_length = 0;
        
        if (s_current_event.event_type != HMI_EVENT_NONE) {
            return &s_current_event;
        }
    }
    
    return NULL;
}

/**
  * @brief  HMI主循环处理函数
  * @param  None
  * @retval None
  */
void HMI_MainLoop(void)
{
    static uint32_t last_update = 0;
    uint32_t current_time = SysTick_GetTick();
    
    // 每100ms更新一次显示
    if (current_time - last_update >= 100) {
        last_update = current_time;
        g_hmi_status.last_update_time = current_time;
        
        // 可以在这里添加定期更新逻辑
    }
}

/**
  * @brief  USART2中断处理函数
  * @param  None
  * @retval None
  */
void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(s_hmi_usart, USART_IT_RXNE) != RESET) {
        uint8_t received_byte = USART_ReceiveData(s_hmi_usart);
        
        if (g_hmi_status.rx_length < HMI_RX_BUFFER_SIZE - 1) {
            g_hmi_status.rx_buffer[g_hmi_status.rx_length++] = received_byte;
            
            // 检查是否接收到完整命令 (简化检测)
            if (received_byte == '\n' || received_byte == '\r') {
                g_hmi_status.rx_complete = true;
                g_hmi_status.rx_count++;
            }
        } else {
            // 缓冲区溢出，重置
            g_hmi_status.rx_length = 0;
        }
        
        USART_ClearITPendingBit(s_hmi_usart, USART_IT_RXNE);
    }
}
