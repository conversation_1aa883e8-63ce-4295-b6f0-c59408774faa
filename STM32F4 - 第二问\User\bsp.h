#ifndef __BSP_H
#define __BSP_H

#include "stm32f4xx.h"

// G题项目硬件引脚定义

// 1. DAC8552 (使用 SPI3，避免与USB OTG冲突)
#define DAC8552_SPI_PORT         GPIOC
#define DAC8552_SPI_CLK          RCC_AHB1Periph_GPIOC
#define DAC8552_SCK_PIN          GPIO_Pin_10   // SPI3_SCK
#define DAC8552_MOSI_PIN         GPIO_Pin_12   // SPI3_MOSI
#define DAC8552_SCK_PINSOURCE    GPIO_PinSource10
#define DAC8552_MOSI_PINSOURCE   GPIO_PinSource12
#define DAC8552_SPI_AF           GPIO_AF_SPI3
#define DAC8552_SPI_PERIPH       SPI3

#define DAC8552_SYNC_PORT        GPIOC
#define DAC8552_SYNC_CLK         RCC_AHB1Periph_GPIOC
#define DAC8552_SYNC_PIN         GPIO_Pin_11   // 片选信号

// 2. AD7606 (使用 SPI1)
#define AD7606_SPI_PORT          GPIOA
#define AD7606_SPI_CLK           RCC_AHB1Periph_GPIOA
#define AD7606_SCK_PIN           GPIO_Pin_5
#define AD7606_MISO_PIN          GPIO_Pin_6
#define AD7606_SCK_PINSOURCE     GPIO_PinSource5
#define AD7606_MISO_PINSOURCE    GPIO_PinSource6
#define AD7606_SPI_AF            GPIO_AF_SPI1
#define AD7606_SPI_PERIPH        SPI1

#define AD7606_CS_PORT           GPIOA
#define AD7606_CS_CLK            RCC_AHB1Periph_GPIOA
#define AD7606_CS_PIN            GPIO_Pin_15

#define AD7606_CTRL_PORT         GPIOC
#define AD7606_CTRL_CLK          RCC_AHB1Periph_GPIOC
#define AD7606_CONVST_PIN        GPIO_Pin_7
#define AD7606_BUSY_PIN          GPIO_Pin_6
#define AD7606_RESET_PIN         GPIO_Pin_8

// 3. CD4052 增益控制
#define CD4052_CTRL_PORT         GPIOE
#define CD4052_CTRL_CLK          RCC_AHB1Periph_GPIOE
#define CD4052_A_PIN             GPIO_Pin_2
#define CD4052_B_PIN             GPIO_Pin_3

// 函数声明
void BSP_Init(void);

#endif
