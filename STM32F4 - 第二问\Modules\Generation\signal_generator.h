/**
  ******************************************************************************
  * @file    signal_generator.h
  * <AUTHOR>
  * @version V1.0
  * @date    2025-01-01
  * @brief   双模块信号发生器统一控制接口 - 电赛G题专用
  *          集成DAC8552和AD9834，实现智能频率分段控制
  ******************************************************************************
  * @attention
  * 
  * 技术方案：
  * - 低频段 (100Hz-10kHz): DAC8552模块
  * - 高频段 (10kHz-5MHz): AD9834模块
  * - 10kHz频率点自动切换，带滞回防抖
  * 
  * 功能特性：
  * - 统一API接口，屏蔽底层模块差异
  * - 智能模块选择和切换
  * - 幅度补偿和统一控制
  * - 实时状态监控
  *
  ******************************************************************************
  */

#ifndef __SIGNAL_GENERATOR_H
#define __SIGNAL_GENERATOR_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  信号发生器模块枚举
  */
typedef enum {
    SIGGEN_MODULE_DAC8552 = 0,   ///< DAC8552模块 (低频段)
    SIGGEN_MODULE_AD9834 = 1,    ///< AD9834模块 (高频段)
    SIGGEN_MODULE_COUNT
} SigGen_Module_t;

/**
  * @brief  波形类型枚举
  */
typedef enum {
    SIGGEN_WAVE_SINE = 0,        ///< 正弦波
    SIGGEN_WAVE_SQUARE = 1,      ///< 方波
    SIGGEN_WAVE_TRIANGLE = 2,    ///< 三角波
    SIGGEN_WAVE_COUNT
} SigGen_WaveType_t;

/**
  * @brief  信号发生器配置结构体
  */
typedef struct {
    uint32_t frequency;          ///< 频率 (Hz)
    float amplitude;             ///< 峰峰值 (V)
    SigGen_WaveType_t wave_type; ///< 波形类型
    bool output_enable;          ///< 输出使能
} SigGen_Config_t;

/**
  * @brief  信号发生器状态结构体
  */
typedef struct {
    SigGen_Module_t active_module;    ///< 当前活动模块
    SigGen_Config_t config;           ///< 当前配置
    bool initialized;                 ///< 初始化状态
    uint32_t switch_count;            ///< 模块切换次数
    uint32_t last_switch_time;        ///< 最后切换时间
    float actual_amplitude;           ///< 实际输出幅度
} SigGen_Status_t;

/**
  * @brief  频率补偿表项结构体
  */
typedef struct {
    uint32_t frequency;          ///< 频率 (Hz)
    float compensation_factor;   ///< 补偿因子
} FreqCompensation_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup SigGen_Exported_Constants 信号发生器导出常量
  * @{
  */

// 频率范围定义
#define SIGGEN_FREQ_MIN             100U         ///< 最小频率 (Hz)
#define SIGGEN_FREQ_MAX             5000000U     ///< 最大频率 (Hz)
#define SIGGEN_SWITCH_FREQ          10000U       ///< 模块切换频率 (Hz)
#define SIGGEN_HYSTERESIS_FREQ      500U         ///< 滞回频率 (Hz)

// 幅度范围定义
#define SIGGEN_AMP_MIN              1.0f         ///< 最小幅度 (V)
#define SIGGEN_AMP_MAX              5.0f         ///< 最大幅度 (V)
#define SIGGEN_AMP_STEP             0.1f         ///< 幅度步长 (V)

// DAC8552频率范围
#define DAC8552_FREQ_MIN            100U         ///< DAC8552最小频率
#define DAC8552_FREQ_MAX            10000U       ///< DAC8552最大频率

// AD9834频率范围
#define AD9834_FREQ_MIN             10000U       ///< AD9834最小频率
#define AD9834_FREQ_MAX             5000000U     ///< AD9834最大频率

// 默认配置
#define SIGGEN_DEFAULT_FREQ         1000U        ///< 默认频率
#define SIGGEN_DEFAULT_AMP          2.5f         ///< 默认幅度
#define SIGGEN_DEFAULT_WAVE         SIGGEN_WAVE_SINE  ///< 默认波形

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup SigGen_Exported_Variables 信号发生器导出变量
  * @{
  */

extern SigGen_Status_t g_siggen_status;         ///< 信号发生器状态

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup SigGen_Exported_Functions 信号发生器导出函数
  * @{
  */

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_Init(void);

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SigGen_DeInit(void);

/**
  * @brief  设置输出频率
  * @param  frequency: 频率 (Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetFrequency(uint32_t frequency);

/**
  * @brief  设置输出幅度
  * @param  amplitude: 峰峰值 (V)
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetAmplitude(float amplitude);

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetWaveType(SigGen_WaveType_t wave_type);

/**
  * @brief  使能/禁用输出
  * @param  enable: true-使能, false-禁用
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetOutputEnable(bool enable);

/**
  * @brief  获取当前配置
  * @param  config: 配置结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_GetConfig(SigGen_Config_t* config);

/**
  * @brief  获取当前状态
  * @param  status: 状态结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_GetStatus(SigGen_Status_t* status);

/**
  * @brief  获取当前活动模块
  * @param  None
  * @retval 活动模块类型
  */
SigGen_Module_t SigGen_GetActiveModule(void);

/**
  * @brief  强制切换模块 (调试用)
  * @param  module: 目标模块
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_ForceSwitch(SigGen_Module_t module);

/**
  * @brief  扫频功能
  * @param  start_freq: 起始频率 (Hz)
  * @param  stop_freq: 结束频率 (Hz)
  * @param  step_freq: 频率步长 (Hz)
  * @param  dwell_time_ms: 每个频率点停留时间 (ms)
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_FrequencySweep(uint32_t start_freq, uint32_t stop_freq, 
                             uint32_t step_freq, uint32_t dwell_time_ms);

/**
  * @brief  停止扫频
  * @param  None
  * @retval None
  */
void SigGen_StopSweep(void);

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SigGen_MainLoop(void);

/**
  * @brief  获取模块名称字符串
  * @param  module: 模块类型
  * @retval 模块名称字符串
  */
const char* SigGen_GetModuleName(SigGen_Module_t module);

/**
  * @brief  获取波形名称字符串
  * @param  wave_type: 波形类型
  * @retval 波形名称字符串
  */
const char* SigGen_GetWaveName(SigGen_WaveType_t wave_type);

/**
  * @brief  自检功能
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SelfTest(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __SIGNAL_GENERATOR_H */

/************************ (C) COPYRIGHT STM32F4电赛项目组 *****END OF FILE****/
