<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>ITM_Type Struct Reference</title>
<title>CMSIS-CORE: ITM_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_i_t_m___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">ITM_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Instrumentation Trace Macrocell Register (ITM).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:afe056e8c8f8c5519d9b47611fa3a4c46"><td class="memItemLeft" >union {</td></tr>
<tr class="memitem:afb9840e1a9a8abc9e175ff4593bb06c2"><td class="memItemLeft" >&#160;&#160;&#160;__O uint8_t&#160;&#160;&#160;<a class="el" href="struct_i_t_m___type.html#abea77b06775d325e5f6f46203f582433">u8</a></td></tr>
<tr class="memdesc:afb9840e1a9a8abc9e175ff4593bb06c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 ( /W) ITM Stimulus Port 8-bit.  <a href="#afb9840e1a9a8abc9e175ff4593bb06c2"></a><br/></td></tr>
<tr class="separator:afb9840e1a9a8abc9e175ff4593bb06c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ff70d74e2edac38f1b2222e8165f566"><td class="memItemLeft" >&#160;&#160;&#160;__O uint16_t&#160;&#160;&#160;<a class="el" href="struct_i_t_m___type.html#a12aa4eb4d9dcb589a5d953c836f4e8f4">u16</a></td></tr>
<tr class="memdesc:a8ff70d74e2edac38f1b2222e8165f566"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 ( /W) ITM Stimulus Port 16-bit.  <a href="#a8ff70d74e2edac38f1b2222e8165f566"></a><br/></td></tr>
<tr class="separator:a8ff70d74e2edac38f1b2222e8165f566"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a173603927cc7e19b6c205ffb0fee3627"><td class="memItemLeft" >&#160;&#160;&#160;__O uint32_t&#160;&#160;&#160;<a class="el" href="struct_i_t_m___type.html#a6882fa5af67ef5c5dfb433b3b68939df">u32</a></td></tr>
<tr class="memdesc:a173603927cc7e19b6c205ffb0fee3627"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 ( /W) ITM Stimulus Port 32-bit.  <a href="#a173603927cc7e19b6c205ffb0fee3627"></a><br/></td></tr>
<tr class="separator:a173603927cc7e19b6c205ffb0fee3627"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe056e8c8f8c5519d9b47611fa3a4c46"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#afe056e8c8f8c5519d9b47611fa3a4c46">PORT</a> [32]</td></tr>
<tr class="memdesc:afe056e8c8f8c5519d9b47611fa3a4c46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 ( /W) ITM Stimulus Port Registers.  <a href="#afe056e8c8f8c5519d9b47611fa3a4c46"></a><br/></td></tr>
<tr class="separator:afe056e8c8f8c5519d9b47611fa3a4c46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c5ae30385b5f370d023468ea9914c0e"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#a2c5ae30385b5f370d023468ea9914c0e">RESERVED0</a> [864]</td></tr>
<tr class="memdesc:a2c5ae30385b5f370d023468ea9914c0e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a2c5ae30385b5f370d023468ea9914c0e"></a><br/></td></tr>
<tr class="separator:a2c5ae30385b5f370d023468ea9914c0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91a040e1b162e1128ac1e852b4a0e589"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#a91a040e1b162e1128ac1e852b4a0e589">TER</a></td></tr>
<tr class="memdesc:a91a040e1b162e1128ac1e852b4a0e589"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xE00 (R/W) ITM Trace Enable Register.  <a href="#a91a040e1b162e1128ac1e852b4a0e589"></a><br/></td></tr>
<tr class="separator:a91a040e1b162e1128ac1e852b4a0e589"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afffce5b93bbfedbaee85357d0b07ebce"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#afffce5b93bbfedbaee85357d0b07ebce">RESERVED1</a> [15]</td></tr>
<tr class="memdesc:afffce5b93bbfedbaee85357d0b07ebce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#afffce5b93bbfedbaee85357d0b07ebce"></a><br/></td></tr>
<tr class="separator:afffce5b93bbfedbaee85357d0b07ebce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93b480aac6da620bbb611212186d47fa"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#a93b480aac6da620bbb611212186d47fa">TPR</a></td></tr>
<tr class="memdesc:a93b480aac6da620bbb611212186d47fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xE40 (R/W) ITM Trace Privilege Register.  <a href="#a93b480aac6da620bbb611212186d47fa"></a><br/></td></tr>
<tr class="separator:a93b480aac6da620bbb611212186d47fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af56b2f07bc6b42cd3e4d17e1b27cff7b"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#af56b2f07bc6b42cd3e4d17e1b27cff7b">RESERVED2</a> [15]</td></tr>
<tr class="memdesc:af56b2f07bc6b42cd3e4d17e1b27cff7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#af56b2f07bc6b42cd3e4d17e1b27cff7b"></a><br/></td></tr>
<tr class="separator:af56b2f07bc6b42cd3e4d17e1b27cff7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58f169e1aa40a9b8afb6296677c3bb45"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_i_t_m___type.html#a58f169e1aa40a9b8afb6296677c3bb45">TCR</a></td></tr>
<tr class="memdesc:a58f169e1aa40a9b8afb6296677c3bb45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xE80 (R/W) ITM Trace Control Register.  <a href="#a58f169e1aa40a9b8afb6296677c3bb45"></a><br/></td></tr>
<tr class="separator:a58f169e1aa40a9b8afb6296677c3bb45"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="afe056e8c8f8c5519d9b47611fa3a4c46"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O { ... }    ITM_Type::PORT[32]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2c5ae30385b5f370d023468ea9914c0e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ITM_Type::RESERVED0[864]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afffce5b93bbfedbaee85357d0b07ebce"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ITM_Type::RESERVED1[15]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af56b2f07bc6b42cd3e4d17e1b27cff7b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ITM_Type::RESERVED2[15]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a58f169e1aa40a9b8afb6296677c3bb45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t ITM_Type::TCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a91a040e1b162e1128ac1e852b4a0e589"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t ITM_Type::TER</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a93b480aac6da620bbb611212186d47fa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t ITM_Type::TPR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a12aa4eb4d9dcb589a5d953c836f4e8f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O uint16_t ITM_Type::u16</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6882fa5af67ef5c5dfb433b3b68939df"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O uint32_t ITM_Type::u32</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abea77b06775d325e5f6f46203f582433"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O uint8_t ITM_Type::u8</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_i_t_m___type.html">ITM_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
