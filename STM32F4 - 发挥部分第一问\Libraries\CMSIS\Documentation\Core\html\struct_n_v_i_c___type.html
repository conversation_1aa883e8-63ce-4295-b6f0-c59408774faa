<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>NVIC_Type Struct Reference</title>
<title>CMSIS-CORE: NVIC_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_n_v_i_c___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">NVIC_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Nested Vectored Interrupt Controller (NVIC).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:af90c80b7c2b48e248780b3781e0df80f"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#af90c80b7c2b48e248780b3781e0df80f">ISER</a> [8]</td></tr>
<tr class="memdesc:af90c80b7c2b48e248780b3781e0df80f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/W) Interrupt Set Enable Register.  <a href="#af90c80b7c2b48e248780b3781e0df80f"></a><br/></td></tr>
<tr class="separator:af90c80b7c2b48e248780b3781e0df80f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2de17698945ea49abd58a2d45bdc9c80"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a2de17698945ea49abd58a2d45bdc9c80">RESERVED0</a> [24]</td></tr>
<tr class="memdesc:a2de17698945ea49abd58a2d45bdc9c80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a2de17698945ea49abd58a2d45bdc9c80"></a><br/></td></tr>
<tr class="separator:a2de17698945ea49abd58a2d45bdc9c80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1965a2e68b61d2e2009621f6949211a5"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a1965a2e68b61d2e2009621f6949211a5">ICER</a> [8]</td></tr>
<tr class="memdesc:a1965a2e68b61d2e2009621f6949211a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x080 (R/W) Interrupt Clear Enable Register.  <a href="#a1965a2e68b61d2e2009621f6949211a5"></a><br/></td></tr>
<tr class="separator:a1965a2e68b61d2e2009621f6949211a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d1daf7ab6f2ba83f57ff67ae6f571fe"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a6d1daf7ab6f2ba83f57ff67ae6f571fe">RSERVED1</a> [24]</td></tr>
<tr class="memdesc:a6d1daf7ab6f2ba83f57ff67ae6f571fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a6d1daf7ab6f2ba83f57ff67ae6f571fe"></a><br/></td></tr>
<tr class="separator:a6d1daf7ab6f2ba83f57ff67ae6f571fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf8e38fc2e97316242ddeb7ea959ab90"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#acf8e38fc2e97316242ddeb7ea959ab90">ISPR</a> [8]</td></tr>
<tr class="memdesc:acf8e38fc2e97316242ddeb7ea959ab90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x100 (R/W) Interrupt Set Pending Register.  <a href="#acf8e38fc2e97316242ddeb7ea959ab90"></a><br/></td></tr>
<tr class="separator:acf8e38fc2e97316242ddeb7ea959ab90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0953af43af8ec7fd5869a1d826ce5b72"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a0953af43af8ec7fd5869a1d826ce5b72">RESERVED2</a> [24]</td></tr>
<tr class="memdesc:a0953af43af8ec7fd5869a1d826ce5b72"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a0953af43af8ec7fd5869a1d826ce5b72"></a><br/></td></tr>
<tr class="separator:a0953af43af8ec7fd5869a1d826ce5b72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46241be64208436d35c9a4f8552575c5"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a46241be64208436d35c9a4f8552575c5">ICPR</a> [8]</td></tr>
<tr class="memdesc:a46241be64208436d35c9a4f8552575c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x180 (R/W) Interrupt Clear Pending Register.  <a href="#a46241be64208436d35c9a4f8552575c5"></a><br/></td></tr>
<tr class="separator:a46241be64208436d35c9a4f8552575c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9dd330835dbf21471e7b5be8692d77ab"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a9dd330835dbf21471e7b5be8692d77ab">RESERVED3</a> [24]</td></tr>
<tr class="memdesc:a9dd330835dbf21471e7b5be8692d77ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a9dd330835dbf21471e7b5be8692d77ab"></a><br/></td></tr>
<tr class="separator:a9dd330835dbf21471e7b5be8692d77ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33e917b381e08dabe4aa5eb2881a7c11"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a33e917b381e08dabe4aa5eb2881a7c11">IABR</a> [8]</td></tr>
<tr class="memdesc:a33e917b381e08dabe4aa5eb2881a7c11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x200 (R/W) Interrupt Active bit Register.  <a href="#a33e917b381e08dabe4aa5eb2881a7c11"></a><br/></td></tr>
<tr class="separator:a33e917b381e08dabe4aa5eb2881a7c11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c0e5d507ac3c1bd5cdaaf9bbd177790"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a5c0e5d507ac3c1bd5cdaaf9bbd177790">RESERVED4</a> [56]</td></tr>
<tr class="memdesc:a5c0e5d507ac3c1bd5cdaaf9bbd177790"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a5c0e5d507ac3c1bd5cdaaf9bbd177790"></a><br/></td></tr>
<tr class="separator:a5c0e5d507ac3c1bd5cdaaf9bbd177790"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6524789fedb94623822c3e0a47f3d06c"><td class="memItemLeft" align="right" valign="top">__IO uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a6524789fedb94623822c3e0a47f3d06c">IP</a> [240]</td></tr>
<tr class="memdesc:a6524789fedb94623822c3e0a47f3d06c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x300 (R/W) Interrupt Priority Register (8Bit wide)  <a href="#a6524789fedb94623822c3e0a47f3d06c"></a><br/></td></tr>
<tr class="separator:a6524789fedb94623822c3e0a47f3d06c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f753b4f824270175af045ac99bc12e8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a4f753b4f824270175af045ac99bc12e8">RESERVED5</a> [644]</td></tr>
<tr class="memdesc:a4f753b4f824270175af045ac99bc12e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a4f753b4f824270175af045ac99bc12e8"></a><br/></td></tr>
<tr class="separator:a4f753b4f824270175af045ac99bc12e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b0d7f3131da89c659a2580249432749"><td class="memItemLeft" align="right" valign="top">__O uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_n_v_i_c___type.html#a0b0d7f3131da89c659a2580249432749">STIR</a></td></tr>
<tr class="memdesc:a0b0d7f3131da89c659a2580249432749"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xE00 ( /W) Software Trigger Interrupt Register.  <a href="#a0b0d7f3131da89c659a2580249432749"></a><br/></td></tr>
<tr class="separator:a0b0d7f3131da89c659a2580249432749"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="a33e917b381e08dabe4aa5eb2881a7c11"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t NVIC_Type::IABR[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1965a2e68b61d2e2009621f6949211a5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t NVIC_Type::ICER[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a46241be64208436d35c9a4f8552575c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t NVIC_Type::ICPR[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6524789fedb94623822c3e0a47f3d06c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint8_t NVIC_Type::IP[240]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af90c80b7c2b48e248780b3781e0df80f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t NVIC_Type::ISER[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acf8e38fc2e97316242ddeb7ea959ab90"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t NVIC_Type::ISPR[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2de17698945ea49abd58a2d45bdc9c80"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RESERVED0[24]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0953af43af8ec7fd5869a1d826ce5b72"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RESERVED2[24]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9dd330835dbf21471e7b5be8692d77ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RESERVED3[24]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5c0e5d507ac3c1bd5cdaaf9bbd177790"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RESERVED4[56]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4f753b4f824270175af045ac99bc12e8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RESERVED5[644]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6d1daf7ab6f2ba83f57ff67ae6f571fe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_Type::RSERVED1[24]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0b0d7f3131da89c659a2580249432749"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__O uint32_t NVIC_Type::STIR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_n_v_i_c___type.html">NVIC_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
