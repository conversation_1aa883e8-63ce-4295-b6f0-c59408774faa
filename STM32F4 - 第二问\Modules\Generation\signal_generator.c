/**
  ******************************************************************************
  * @file    signal_generator.c
  * <AUTHOR>
  * @version V1.0
  * @date    2025-01-01
  * @brief   双模块信号发生器统一控制接口实现
  ******************************************************************************
  */

#include "signal_generator.h"
#include "dac8552.h"
#include "ad9834_highperf.h"
#include "../Core/systick.h"
#include <string.h>

/* Private variables ---------------------------------------------------------*/
static bool s_dac8552_initialized = false;
static bool s_ad9834_initialized = false;
static bool s_sweep_active = false;

// 频率补偿表 (基于实测数据)
static const FreqCompensation_t s_freq_compensation_table[] = {
    {10000,   1.00f},   // 10kHz基准
    {50000,   1.02f},   // 50kHz需2%补偿
    {100000,  1.05f},   // 100kHz需5%补偿
    {500000,  1.10f},   // 500kHz需10%补偿
    {1000000, 1.17f},   // 1MHz需17%补偿
    {5000000, 1.25f}    // 5MHz需25%补偿
};

/* Global variables ----------------------------------------------------------*/
SigGen_Status_t g_siggen_status = {0};

/* Private function prototypes -----------------------------------------------*/
static int8_t SigGen_SelectOptimalModule(uint32_t frequency);
static int8_t SigGen_SwitchModule(SigGen_Module_t target_module);
static float SigGen_GetFrequencyCompensation(uint32_t frequency);
static int8_t SigGen_UpdateDAC8552(void);
static int8_t SigGen_UpdateAD9834(void);

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_Init(void)
{
    // 1. 初始化状态结构体
    memset(&g_siggen_status, 0, sizeof(g_siggen_status));
    
    // 2. 设置默认配置
    g_siggen_status.config.frequency = SIGGEN_DEFAULT_FREQ;
    g_siggen_status.config.amplitude = SIGGEN_DEFAULT_AMP;
    g_siggen_status.config.wave_type = SIGGEN_DEFAULT_WAVE;
    g_siggen_status.config.output_enable = false;
    
    // 3. 初始化DAC8552模块
    if (DAC8552_Init() == DAC8552_OK) {
        s_dac8552_initialized = true;
    } else {
        return -1;
    }
    
    // 4. 初始化AD9834模块
    AD9834_Init();
    s_ad9834_initialized = true;
    
    // 5. 选择初始模块
    SigGen_SelectOptimalModule(g_siggen_status.config.frequency);
    
    // 6. 设置初始化标志
    g_siggen_status.initialized = true;
    
    return 0;
}

/**
  * @brief  设置输出频率
  * @param  frequency: 频率 (Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetFrequency(uint32_t frequency)
{
    if (!g_siggen_status.initialized) {
        return -1;
    }
    
    // 频率范围检查
    if (frequency < SIGGEN_FREQ_MIN || frequency > SIGGEN_FREQ_MAX) {
        return -1;
    }
    
    // 选择最优模块
    if (SigGen_SelectOptimalModule(frequency) != 0) {
        return -1;
    }
    
    // 更新配置
    g_siggen_status.config.frequency = frequency;
    
    // 更新对应模块
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
        return SigGen_UpdateDAC8552();
    } else {
        return SigGen_UpdateAD9834();
    }
}

/**
  * @brief  设置输出幅度
  * @param  amplitude: 峰峰值 (V)
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetAmplitude(float amplitude)
{
    if (!g_siggen_status.initialized) {
        return -1;
    }
    
    // 幅度范围检查
    if (amplitude < SIGGEN_AMP_MIN || amplitude > SIGGEN_AMP_MAX) {
        return -1;
    }
    
    // 更新配置
    g_siggen_status.config.amplitude = amplitude;
    
    // 更新对应模块
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
        return SigGen_UpdateDAC8552();
    } else {
        return SigGen_UpdateAD9834();
    }
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetWaveType(SigGen_WaveType_t wave_type)
{
    if (!g_siggen_status.initialized || wave_type >= SIGGEN_WAVE_COUNT) {
        return -1;
    }
    
    // 更新配置
    g_siggen_status.config.wave_type = wave_type;
    
    // 更新对应模块
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
        return SigGen_UpdateDAC8552();
    } else {
        return SigGen_UpdateAD9834();
    }
}

/**
  * @brief  使能/禁用输出
  * @param  enable: true-使能, false-禁用
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_SetOutputEnable(bool enable)
{
    if (!g_siggen_status.initialized) {
        return -1;
    }
    
    g_siggen_status.config.output_enable = enable;
    
    if (!enable) {
        // 禁用输出 - 设置为中点电压
        if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
            DAC8552_Write(DAC8552_CHANNEL_A, 0x8000);  // 中点电压
        } else {
            AD9834_Reset();  // 复位AD9834
        }
    } else {
        // 使能输出 - 恢复正常波形
        if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
            return SigGen_UpdateDAC8552();
        } else {
            return SigGen_UpdateAD9834();
        }
    }
    
    return 0;
}

/**
  * @brief  选择最优模块
  * @param  frequency: 频率 (Hz)
  * @retval 0: 成功, -1: 失败
  */
static int8_t SigGen_SelectOptimalModule(uint32_t frequency)
{
    SigGen_Module_t target_module;
    
    // 带滞回的模块选择逻辑
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
        target_module = (frequency > SIGGEN_SWITCH_FREQ + SIGGEN_HYSTERESIS_FREQ) ? 
                        SIGGEN_MODULE_AD9834 : SIGGEN_MODULE_DAC8552;
    } else {
        target_module = (frequency < SIGGEN_SWITCH_FREQ - SIGGEN_HYSTERESIS_FREQ) ? 
                        SIGGEN_MODULE_DAC8552 : SIGGEN_MODULE_AD9834;
    }
    
    // 执行模块切换
    if (target_module != g_siggen_status.active_module) {
        return SigGen_SwitchModule(target_module);
    }
    
    return 0;
}

/**
  * @brief  切换模块
  * @param  target_module: 目标模块
  * @retval 0: 成功, -1: 失败
  */
static int8_t SigGen_SwitchModule(SigGen_Module_t target_module)
{
    if (target_module >= SIGGEN_MODULE_COUNT) {
        return -1;
    }
    
    // 禁用当前模块输出
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552) {
        DAC8552_Write(DAC8552_CHANNEL_A, 0x8000);  // 中点电压
    } else {
        AD9834_Reset();  // 复位AD9834
    }
    
    // 切换到目标模块
    g_siggen_status.active_module = target_module;
    g_siggen_status.switch_count++;
    g_siggen_status.last_switch_time = SysTick_GetTick();
    
    // 短暂延时确保切换稳定
    Delay_ms(10);
    
    return 0;
}

/**
  * @brief  获取频率补偿因子
  * @param  frequency: 频率 (Hz)
  * @retval 补偿因子
  */
static float SigGen_GetFrequencyCompensation(uint32_t frequency)
{
    // 仅对AD9834进行频率补偿
    if (g_siggen_status.active_module != SIGGEN_MODULE_AD9834) {
        return 1.0f;
    }
    
    // 线性插值查找补偿因子
    uint8_t table_size = sizeof(s_freq_compensation_table) / sizeof(FreqCompensation_t);
    
    for (uint8_t i = 0; i < table_size - 1; i++) {
        if (frequency <= s_freq_compensation_table[i + 1].frequency) {
            // 线性插值
            float f1 = s_freq_compensation_table[i].frequency;
            float f2 = s_freq_compensation_table[i + 1].frequency;
            float c1 = s_freq_compensation_table[i].compensation_factor;
            float c2 = s_freq_compensation_table[i + 1].compensation_factor;
            
            return c1 + (c2 - c1) * (frequency - f1) / (f2 - f1);
        }
    }
    
    // 超出范围，使用最后一个值
    return s_freq_compensation_table[table_size - 1].compensation_factor;
}

/**
  * @brief  更新DAC8552输出
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
static int8_t SigGen_UpdateDAC8552(void)
{
    if (!s_dac8552_initialized || !g_siggen_status.config.output_enable) {
        return -1;
    }
    
    // DAC8552通过软件DDS实现，这里只是示例
    // 实际实现需要在主循环中持续更新
    
    // 更新实际幅度
    g_siggen_status.actual_amplitude = g_siggen_status.config.amplitude;
    
    return 0;
}

/**
  * @brief  更新AD9834输出
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
static int8_t SigGen_UpdateAD9834(void)
{
    if (!s_ad9834_initialized || !g_siggen_status.config.output_enable) {
        return -1;
    }
    
    // 获取频率补偿因子
    float compensation = SigGen_GetFrequencyCompensation(g_siggen_status.config.frequency);
    
    // 转换波形类型
    uint16_t ad9834_wave_type;
    switch (g_siggen_status.config.wave_type) {
        case SIGGEN_WAVE_SINE:
            ad9834_wave_type = SINE_WAVE;
            break;
        case SIGGEN_WAVE_SQUARE:
            ad9834_wave_type = SQUARE_WAVE;
            break;
        case SIGGEN_WAVE_TRIANGLE:
            ad9834_wave_type = TRIANGLE_WAVE;
            break;
        default:
            ad9834_wave_type = SINE_WAVE;
            break;
    }
    
    // 设置AD9834频率和波形
    AD9834_SetFrequency(FREQ_REG_0, (float)g_siggen_status.config.frequency, ad9834_wave_type);
    
    // 更新实际幅度 (考虑补偿)
    g_siggen_status.actual_amplitude = g_siggen_status.config.amplitude / compensation;
    
    return 0;
}

/**
  * @brief  获取当前配置
  * @param  config: 配置结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_GetConfig(SigGen_Config_t* config)
{
    if (config == NULL || !g_siggen_status.initialized) {
        return -1;
    }
    
    *config = g_siggen_status.config;
    return 0;
}

/**
  * @brief  获取当前状态
  * @param  status: 状态结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t SigGen_GetStatus(SigGen_Status_t* status)
{
    if (status == NULL || !g_siggen_status.initialized) {
        return -1;
    }
    
    *status = g_siggen_status;
    return 0;
}

/**
  * @brief  获取当前活动模块
  * @param  None
  * @retval 活动模块类型
  */
SigGen_Module_t SigGen_GetActiveModule(void)
{
    return g_siggen_status.active_module;
}

/**
  * @brief  获取模块名称字符串
  * @param  module: 模块类型
  * @retval 模块名称字符串
  */
const char* SigGen_GetModuleName(SigGen_Module_t module)
{
    switch (module) {
        case SIGGEN_MODULE_DAC8552:
            return "DAC8552";
        case SIGGEN_MODULE_AD9834:
            return "AD9834";
        default:
            return "Unknown";
    }
}

/**
  * @brief  获取波形名称字符串
  * @param  wave_type: 波形类型
  * @retval 波形名称字符串
  */
const char* SigGen_GetWaveName(SigGen_WaveType_t wave_type)
{
    switch (wave_type) {
        case SIGGEN_WAVE_SINE:
            return "Sine";
        case SIGGEN_WAVE_SQUARE:
            return "Square";
        case SIGGEN_WAVE_TRIANGLE:
            return "Triangle";
        default:
            return "Unknown";
    }
}

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SigGen_MainLoop(void)
{
    // DAC8552需要在主循环中持续更新
    if (g_siggen_status.active_module == SIGGEN_MODULE_DAC8552 && 
        g_siggen_status.config.output_enable) {
        // 这里应该调用DAC8552的波形更新函数
        // SineGen_Update();  // 需要根据实际实现调整
    }
}
