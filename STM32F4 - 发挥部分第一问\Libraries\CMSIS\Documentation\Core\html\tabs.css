.tabs, .tabs1, .tabs2, .tabs3 {
    background-image: url('tab_b.png');
    width: 100%;
    z-index: 101;
    font-size: 10px;
}

.tabs1 {
    background-image: url('tab_topnav.png');
    font-size: 12px;
}

.tabs2 {
    font-size: 10px;
}
.tabs3 {
    font-size: 9px;
}

.tablist {
    margin: 0;
    padding: 0;
    display: table;
    line-height: 24px;
}

.tablist li {
    float: left;
    display: table-cell;
    background-image: url('tab_b.png');
    list-style: none;
}

.tabs1 .tablist li {
    float: left;
    display: table-cell;
    background-image: url('tab_topnav.png');
    list-style: none;
}

.tablist a {
    display: block;
    padding: 0 20px;
    font-weight: bold;
    background-image:url('tab_s.png');
    background-repeat:no-repeat;
    background-position:right;
    color: #283A5D;
    text-shadow: 0px 1px 1px rgba(255, 255, 255, 0.9);
    text-decoration: none;
    outline: none;
}

.tabs3 .tablist a {
    padding: 0 10px;
}

.tablist a:hover {
    background-image: url('tab_h.png');
    background-repeat:repeat-x;
    color: #fff;
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 1.0);
    text-decoration: none;
}

.tablist li.current a {
    background-image: url('tab_a.png');
    background-repeat:repeat-x;
    color: #fff;
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 1.0);
}
