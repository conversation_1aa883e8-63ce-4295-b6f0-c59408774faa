Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(.text) refers to _printf_str.o(.text) for _printf_str
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(.text) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(.text) refers to usart.o(.data) for USART_RX_STA
    main.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    main.o(.text) refers to main.o(.data) for test_float
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing led.o(.text), (80 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(.text), (12 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

4 unused section(s) (total 130 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001a4   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000014  0x080001aa   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x080001b0   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001b4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001b6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001b8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001ba   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001bc   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001bc   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001c2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001c2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001c6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001c6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ce   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001d0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001d0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001d4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001dc   Section        0  main.o(.text)
    .text                                    0x08000304   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000320   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000321   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080003f7   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x08000500   Section        0  delay.o(.text)
    .text                                    0x080005d4   Section        0  usart.o(.text)
    .text                                    0x08000714   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000754   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000ab0   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08000e54   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x0800125c   Section        0  misc.o(.text)
    .text                                    0x08001338   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800133c   Section        0  noretval__2printf.o(.text)
    .text                                    0x08001354   Section        0  __printf.o(.text)
    .text                                    0x080013bc   Section        0  _printf_str.o(.text)
    .text                                    0x08001410   Section        0  _printf_dec.o(.text)
    .text                                    0x08001488   Section        0  heapauxi.o(.text)
    .text                                    0x0800148e   Section        2  use_no_semi.o(.text)
    .text                                    0x08001490   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08001542   Section        0  _printf_char.o(.text)
    .text                                    0x08001570   Section        0  _printf_char_file.o(.text)
    .text                                    0x08001594   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001595   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080015c4   Section        0  ferror.o(.text)
    .text                                    0x080015cc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001616   Section        0  exit.o(.text)
    .text                                    0x08001624   Section        8  libspace.o(.text)
    x$fpl$d2f                                0x0800162c   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08001690   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080016a1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dnaninf                            0x080017e0   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800187c   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$dsub                               0x08001888   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08001899   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08001a5c   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$ffix                               0x08001ab4   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fmul                               0x08001aec   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08001bee   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08001c7a   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$usenofp                            0x08001c84   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000001c   Section        4  delay.o(.data)
    fac_us                                   0x2000001c   Data           1  delay.o(.data)
    fac_ms                                   0x2000001e   Data           2  delay.o(.data)
    .data                                    0x20000020   Section        6  usart.o(.data)
    .data                                    0x20000026   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000026   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000036   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x2000003c   Section      200  usart.o(.bss)
    .bss                                     0x20000104   Section       96  libspace.o(.bss)
    HEAP                                     0x20000168   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000168   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000368   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000368   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000768   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080001a5   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_s                                0x080001ab   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x080001b1   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001b5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001b9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001bd   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001bd   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001c3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001c3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001cf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001d1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001d1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001d5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    HMI_send_string                          0x080001dd   Thumb Code    18  main.o(.text)
    HMI_send_number                          0x080001ef   Thumb Code    18  main.o(.text)
    HMI_send_float                           0x08000201   Thumb Code    38  main.o(.text)
    main                                     0x08000227   Thumb Code   138  main.o(.text)
    NMI_Handler                              0x08000305   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x08000307   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x0800030b   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x0800030f   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000313   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x08000317   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x08000319   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x0800031b   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x0800031d   Thumb Code     2  stm32f10x_it.o(.text)
    SystemInit                               0x080003ff   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800044d   Thumb Code   142  system_stm32f10x.o(.text)
    delay_init                               0x08000501   Thumb Code    50  delay.o(.text)
    delay_us                                 0x08000533   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x0800057b   Thumb Code    72  delay.o(.text)
    _sys_exit                                0x080005d5   Thumb Code     6  usart.o(.text)
    fputc                                    0x080005db   Thumb Code    24  usart.o(.text)
    uart_init                                0x080005f3   Thumb Code   152  usart.o(.text)
    USART1_IRQHandler                        0x0800068b   Thumb Code   122  usart.o(.text)
    Reset_Handler                            0x08000715   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800072f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000731   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    GPIO_DeInit                              0x08000755   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000801   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000815   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x0800092b   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x0800093b   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x0800094d   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000955   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000967   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x0800096f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08000973   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08000977   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08000981   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000985   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000997   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x080009b1   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x080009b7   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08000a41   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08000a83   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08000ab1   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08000af1   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000b37   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000b6f   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000ba7   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000bbb   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08000bc1   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000bd9   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000bdf   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000bf1   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08000bfb   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08000c0d   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08000c1f   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08000c33   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08000c4d   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08000c55   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08000c67   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000c99   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000c9f   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000cab   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000cb3   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08000d73   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000d8d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000da7   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000dc1   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000ddb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000df5   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000dfd   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08000e03   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08000e09   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08000e17   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000e2b   Thumb Code     6  stm32f10x_rcc.o(.text)
    USART_DeInit                             0x08000e55   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08000edb   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08000fad   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08000fc5   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08000fe7   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08000ff3   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x0800100b   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08001055   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08001067   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08001079   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x0800108b   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x080010a3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x080010b5   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x080010cd   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x080010d5   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x080010df   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x080010e9   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x080010f9   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08001109   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001121   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001139   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001151   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001167   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x0800117f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08001191   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x080011a9   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x080011c3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x080011d5   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001229   Thumb Code    52  stm32f10x_usart.o(.text)
    NVIC_PriorityGroupConfig                 0x0800125d   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08001267   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x080012cb   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080012d9   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080012fb   Thumb Code    40  misc.o(.text)
    __use_no_semihosting                     0x08001339   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800133d   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x08001355   Thumb Code   104  __printf.o(.text)
    _printf_str                              0x080013bd   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08001411   Thumb Code   104  _printf_dec.o(.text)
    __use_two_region_memory                  0x08001489   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800148b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800148d   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800148f   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800148f   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08001491   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_cs_common                        0x08001543   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001557   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001567   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08001571   Thumb Code    32  _printf_char_file.o(.text)
    _printf_char_common                      0x0800159f   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x080015c5   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x080015cd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001617   Thumb Code    12  exit.o(.text)
    __user_libspace                          0x08001625   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001625   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001625   Thumb Code     0  libspace.o(.text)
    __aeabi_d2f                              0x0800162d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800162d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08001691   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08001691   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dnaninf                            0x080017e1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800187d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_dsub                             0x08001889   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08001889   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08001a5d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08001a5d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_f2iz                             0x08001ab5   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08001ab5   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_fmul                             0x08001aed   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08001aed   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08001bef   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08001c7b   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    Region$$Table$$Base                      0x08001c84   Number         0  anon$$obj.o(Region$$Table)
    __I$use$fp                               0x08001c84   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Limit                     0x08001ca4   Number         0  anon$$obj.o(Region$$Table)
    test_num                                 0x20000000   Data           4  main.o(.data)
    test_float                               0x20000004   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000020   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000024   Data           2  usart.o(.data)
    USART_RX_BUF                             0x2000003c   Data         200  usart.o(.bss)
    __libspace_start                         0x20000104   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000164   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001ce0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00001ca4, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO          258    RESET               startup_stm32f10x_hd.o
    0x08000130   0x00000008   Code   RO          352  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO          546    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO          548    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO          550    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000000   Code   RO          349    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x00000006   Code   RO          348    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001aa   0x00000006   Code   RO          347    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001b0   0x00000004   Code   RO          379    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001b4   0x00000002   Code   RO          421    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b6   0x00000000   Code   RO          428    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          430    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          433    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          435    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          437    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          440    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          442    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          444    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          446    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          448    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          450    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          452    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          454    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          456    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          458    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          460    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          464    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          466    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          468    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001b6   0x00000000   Code   RO          470    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001b6   0x00000002   Code   RO          471    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001b8   0x00000002   Code   RO          489    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001ba   0x00000000   Code   RO          500    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001ba   0x00000000   Code   RO          503    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001ba   0x00000000   Code   RO          506    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001ba   0x00000000   Code   RO          508    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001ba   0x00000000   Code   RO          511    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001ba   0x00000002   Code   RO          512    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001bc   0x00000000   Code   RO          372    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001bc   0x00000000   Code   RO          390    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001bc   0x00000006   Code   RO          402    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001c2   0x00000000   Code   RO          392    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001c2   0x00000004   Code   RO          393    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001c6   0x00000000   Code   RO          395    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001c6   0x00000008   Code   RO          396    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ce   0x00000002   Code   RO          425    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001d0   0x00000000   Code   RO          473    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001d0   0x00000004   Code   RO          474    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001d4   0x00000006   Code   RO          475    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001da   0x00000002   PAD
    0x080001dc   0x00000128   Code   RO            1    .text               main.o
    0x08000304   0x0000001a   Code   RO          118    .text               stm32f10x_it.o
    0x0800031e   0x00000002   PAD
    0x08000320   0x000001e0   Code   RO          159    .text               system_stm32f10x.o
    0x08000500   0x000000d4   Code   RO          192    .text               delay.o
    0x080005d4   0x00000140   Code   RO          223    .text               usart.o
    0x08000714   0x00000040   Code   RO          259    .text               startup_stm32f10x_hd.o
    0x08000754   0x0000035c   Code   RO          263    .text               stm32f10x_gpio.o
    0x08000ab0   0x000003a4   Code   RO          275    .text               stm32f10x_rcc.o
    0x08000e54   0x00000408   Code   RO          289    .text               stm32f10x_usart.o
    0x0800125c   0x000000dc   Code   RO          301    .text               misc.o
    0x08001338   0x00000002   Code   RO          315    .text               c_w.l(use_no_semi_2.o)
    0x0800133a   0x00000002   PAD
    0x0800133c   0x00000018   Code   RO          319    .text               c_w.l(noretval__2printf.o)
    0x08001354   0x00000068   Code   RO          321    .text               c_w.l(__printf.o)
    0x080013bc   0x00000052   Code   RO          323    .text               c_w.l(_printf_str.o)
    0x0800140e   0x00000002   PAD
    0x08001410   0x00000078   Code   RO          325    .text               c_w.l(_printf_dec.o)
    0x08001488   0x00000006   Code   RO          350    .text               c_w.l(heapauxi.o)
    0x0800148e   0x00000002   Code   RO          370    .text               c_w.l(use_no_semi.o)
    0x08001490   0x000000b2   Code   RO          373    .text               c_w.l(_printf_intcommon.o)
    0x08001542   0x0000002c   Code   RO          375    .text               c_w.l(_printf_char.o)
    0x0800156e   0x00000002   PAD
    0x08001570   0x00000024   Code   RO          377    .text               c_w.l(_printf_char_file.o)
    0x08001594   0x00000030   Code   RO          404    .text               c_w.l(_printf_char_common.o)
    0x080015c4   0x00000008   Code   RO          406    .text               c_w.l(ferror.o)
    0x080015cc   0x0000004a   Code   RO          410    .text               c_w.l(sys_stackheap_outer.o)
    0x08001616   0x0000000c   Code   RO          414    .text               c_w.l(exit.o)
    0x08001622   0x00000002   PAD
    0x08001624   0x00000008   Code   RO          422    .text               c_w.l(libspace.o)
    0x0800162c   0x00000062   Code   RO          354    x$fpl$d2f           fz_ws.l(d2f.o)
    0x0800168e   0x00000002   PAD
    0x08001690   0x00000150   Code   RO          356    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x080017e0   0x0000009c   Code   RO          380    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x0800187c   0x0000000c   Code   RO          382    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08001888   0x000001d4   Code   RO          358    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08001a5c   0x00000056   Code   RO          362    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08001ab2   0x00000002   PAD
    0x08001ab4   0x00000036   Code   RO          364    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08001aea   0x00000002   PAD
    0x08001aec   0x00000102   Code   RO          368    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08001bee   0x0000008c   Code   RO          384    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08001c7a   0x0000000a   Code   RO          386    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08001c84   0x00000000   Code   RO          388    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08001c84   0x00000020   Data   RO          544    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000768, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000008   Data   RW            2    .data               main.o
    0x20000008   0x00000014   Data   RW          160    .data               system_stm32f10x.o
    0x2000001c   0x00000004   Data   RW          193    .data               delay.o
    0x20000020   0x00000006   Data   RW          225    .data               usart.o
    0x20000026   0x00000014   Data   RW          276    .data               stm32f10x_rcc.o
    0x2000003a   0x00000002   PAD
    0x2000003c   0x000000c8   Zero   RW          224    .bss                usart.o
    0x20000104   0x00000060   Zero   RW          423    .bss                c_w.l(libspace.o)
    0x20000164   0x00000004   PAD
    0x20000168   0x00000200   Zero   RW          257    HEAP                startup_stm32f10x_hd.o
    0x20000368   0x00000400   Zero   RW          256    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       212         18          0          4          0        983   delay.o
       296         84          0          8          0     232268   main.o
       220         22          0          0          0       1761   misc.o
        64         26        304          0       1536        776   startup_stm32f10x_hd.o
       860         38          0          0          0       5605   stm32f10x_gpio.o
        26          0          0          0          0       1158   stm32f10x_it.o
       932         36          0         20          0       8752   stm32f10x_rcc.o
      1032         22          0          0          0       8328   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       480         38          0         20          0       1679   system_stm32f10x.o
       320         16          0          6        200       3086   usart.o

    ----------------------------------------------------------------------
      4444        <USER>        <GROUP>         60       1736     264428   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        12          0          0          0          0         72   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       804         16          0          0          0        272   daddsub_clz.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
        54          4          0          0          0         84   ffix.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      2552         <USER>          <GROUP>          0        100       2284   Library Totals
        18          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       916         42          0          0         96       1356   c_w.l
      1618         40          0          0          0        928   fz_ws.l

    ----------------------------------------------------------------------
      2552         <USER>          <GROUP>          0        100       2284   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6996        382        336         60       1836     264888   Grand Totals
      6996        382        336         60       1836     264888   ELF Image Totals
      6996        382        336         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7332 (   7.16kB)
    Total RW  Size (RW Data + ZI Data)              1896 (   1.85kB)
    Total ROM Size (Code + RO Data + RW Data)       7392 (   7.22kB)

==============================================================================

