<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Globals</title>
<title>CMSIS-CORE: Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index__"><span>_</span></a></li>
      <li><a href="#index_b"><span>b</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_h"><span>h</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_n"><span>n</span></a></li>
      <li><a href="#index_p"><span>p</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li><a href="#index_u"><span>u</span></a></li>
      <li><a href="#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('globals.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:</div>

<h3><a class="anchor" id="index__"></a>- _ -</h3><ul>
<li>__BKPT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga92f5621626711931da71eaa8bf301af7">Ref_cmInstr.txt</a>
</li>
<li>__CLREX()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga354c5ac8870cc3dfb823367af9c4b412">Ref_cmInstr.txt</a>
</li>
<li>__CLZ()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga90884c591ac5d73d6069334eba9d6c02">Ref_cmInstr.txt</a>
</li>
<li>__disable_fault_irq()
: <a class="el" href="group___core___register__gr.html#ga9d174f979b2f76fdb3228a9b338fd939">Ref_CoreReg.txt</a>
</li>
<li>__disable_irq()
: <a class="el" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013">Ref_CoreReg.txt</a>
</li>
<li>__DMB()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gab1c9b393641dc2d397b3408fdbe72b96">Ref_cmInstr.txt</a>
</li>
<li>__DSB()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gacb2a8ca6eae1ba4b31161578b720c199">Ref_cmInstr.txt</a>
</li>
<li>__enable_fault_irq()
: <a class="el" href="group___core___register__gr.html#ga6575d37863cec5d334864f93b5b783bf">Ref_CoreReg.txt</a>
</li>
<li>__enable_irq()
: <a class="el" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27">Ref_CoreReg.txt</a>
</li>
<li>__get_APSR()
: <a class="el" href="group___core___register__gr.html#ga811c0012221ee918a75111ca84c4d5e7">Ref_CoreReg.txt</a>
</li>
<li>__get_BASEPRI()
: <a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667">Ref_CoreReg.txt</a>
</li>
<li>__get_CONTROL()
: <a class="el" href="group___core___register__gr.html#ga963cf236b73219ce78e965deb01b81a7">Ref_CoreReg.txt</a>
</li>
<li>__get_FAULTMASK()
: <a class="el" href="group___core___register__gr.html#gaa78e4e6bf619a65e9f01b4af13fed3a8">Ref_CoreReg.txt</a>
</li>
<li>__get_FPSCR()
: <a class="el" href="group___core___register__gr.html#gad6d7eca9ddd1d9072dd7b020cfe64905">Ref_CoreReg.txt</a>
</li>
<li>__get_IPSR()
: <a class="el" href="group___core___register__gr.html#ga2c32fc5c7f8f07fb3d436c6f6fe4e8c8">Ref_CoreReg.txt</a>
</li>
<li>__get_MSP()
: <a class="el" href="group___core___register__gr.html#gab898559392ba027814e5bbb5a98b38d2">Ref_CoreReg.txt</a>
</li>
<li>__get_PRIMASK()
: <a class="el" href="group___core___register__gr.html#ga799b5d9a2ae75e459264c8512c7c0e02">Ref_CoreReg.txt</a>
</li>
<li>__get_PSP()
: <a class="el" href="group___core___register__gr.html#ga914dfa8eff7ca53380dd54cf1d8bebd9">Ref_CoreReg.txt</a>
</li>
<li>__get_xPSR()
: <a class="el" href="group___core___register__gr.html#ga732e08184154f44a617963cc65ff95bd">Ref_CoreReg.txt</a>
</li>
<li>__ISB()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga93c09b4709394d81977300d5f84950e5">Ref_cmInstr.txt</a>
</li>
<li>__LDRBT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9464d75db32846aa8295c3c3adfacb41">Ref_cmInstr.txt</a>
</li>
<li>__LDREXB()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9e3ac13d8dcf4331176b624cf6234a7e">Ref_cmInstr.txt</a>
</li>
<li>__LDREXH()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9feffc093d6f68b120d592a7a0d45a15">Ref_cmInstr.txt</a>
</li>
<li>__LDREXW()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gabd78840a0f2464905b7cec791ebc6a4c">Ref_cmInstr.txt</a>
</li>
<li>__LDRHT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gaa762b8bc5634ce38cb14d62a6b2aee32">Ref_cmInstr.txt</a>
</li>
<li>__LDRT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga616504f5da979ba8a073d428d6e8d5c7">Ref_cmInstr.txt</a>
</li>
<li>__NOP()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gac71fad9f0a91980fecafcb450ee0a63e">Ref_cmInstr.txt</a>
</li>
<li>__PKHBT()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaefb8ebf3a54e197464da1ff69a44f4b5">Ref_cm4_simd.txt</a>
</li>
<li>__PKHTB()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafd8fe4a6d87e947caa81a69ec36c1666">Ref_cm4_simd.txt</a>
</li>
<li>__QADD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga17b873f246c9f5e9355760ffef3dad4a">Ref_cm4_simd.txt</a>
</li>
<li>__QADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae83a53ec04b496304bed6d9fe8f7461b">Ref_cm4_simd.txt</a>
</li>
<li>__QADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf2f5a9132dcfc6d01d34cd971c425713">Ref_cm4_simd.txt</a>
</li>
<li>__QASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga87618799672e1511e33964bc71467eb3">Ref_cm4_simd.txt</a>
</li>
<li>__QSAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab41eb2b17512ab01d476fc9d5bd19520">Ref_cm4_simd.txt</a>
</li>
<li>__QSUB()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga3ba259f8f05a36f7b88b469a71ffc096">Ref_cm4_simd.txt</a>
</li>
<li>__QSUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad089605c16df9823a2c8aaa37777aae5">Ref_cm4_simd.txt</a>
</li>
<li>__QSUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga753493a65493880c28baa82c151a0d61">Ref_cm4_simd.txt</a>
</li>
<li>__RBIT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gad6f9f297f6b91a995ee199fbc796b863">Ref_cmInstr.txt</a>
</li>
<li>__REV()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga4717abc17af5ba29b1e4c055e0a0d9b8">Ref_cmInstr.txt</a>
</li>
<li>__REV16()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gaeef6f853b6df3a365c838ee5b49a7a26">Ref_cmInstr.txt</a>
</li>
<li>__REVSH()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga1ec006e6d79063363cb0c2a2e0b3adbe">Ref_cmInstr.txt</a>
</li>
<li>__ROR()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gaf66beb577bb9d90424c3d1d7f684c024">Ref_cmInstr.txt</a>
</li>
<li>__RRX()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gac09134f1bf9c49db07282001afcc9380">Ref_cmInstr.txt</a>
</li>
<li>__SADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad0bf46373a1c05aabf64517e84be5984">Ref_cm4_simd.txt</a>
</li>
<li>__SADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac20aa0f741d0a1494d58c531e38d5785">Ref_cm4_simd.txt</a>
</li>
<li>__SASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5845084fd99c872e98cf5553d554de2a">Ref_cm4_simd.txt</a>
</li>
<li>__SEL()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf5448e591fe49161b6759b48aecb08fe">Ref_cm4_simd.txt</a>
</li>
<li>__set_BASEPRI()
: <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882">Ref_CoreReg.txt</a>
</li>
<li>__set_BASEPRI_MAX()
: <a class="el" href="group___core___register__gr.html#ga62fa63d39cf22df348857d5f44ab64d9">Ref_CoreReg.txt</a>
</li>
<li>__set_CONTROL()
: <a class="el" href="group___core___register__gr.html#gac64d37e7ff9de06437f9fb94bbab8b6c">Ref_CoreReg.txt</a>
</li>
<li>__set_FAULTMASK()
: <a class="el" href="group___core___register__gr.html#gaa5587cc09031053a40a35c14ec36078a">Ref_CoreReg.txt</a>
</li>
<li>__set_FPSCR()
: <a class="el" href="group___core___register__gr.html#ga6f26bd75ca7e3247f27b272acc10536b">Ref_CoreReg.txt</a>
</li>
<li>__set_MSP()
: <a class="el" href="group___core___register__gr.html#ga0bf9564ebc1613a8faba014275dac2a4">Ref_CoreReg.txt</a>
</li>
<li>__set_PRIMASK()
: <a class="el" href="group___core___register__gr.html#ga70b4e1a6c1c86eb913fb9d6e8400156f">Ref_CoreReg.txt</a>
</li>
<li>__set_PSP()
: <a class="el" href="group___core___register__gr.html#ga48e5853f417e17a8a65080f6a605b743">Ref_CoreReg.txt</a>
</li>
<li>__SEV()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga3c34da7eb16496ae2668a5b95fa441e7">Ref_cmInstr.txt</a>
</li>
<li>__SHADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga15d8899a173effb8ad8c7268da32b60e">Ref_cm4_simd.txt</a>
</li>
<li>__SHADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga524575b442ea01aec10c762bf4d85fea">Ref_cm4_simd.txt</a>
</li>
<li>__SHASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae0a649035f67627464fd80e7218c89d5">Ref_cm4_simd.txt</a>
</li>
<li>__SHSAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafadbd89c36b5addcf1ca10dd392db3e9">Ref_cm4_simd.txt</a>
</li>
<li>__SHSUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga31328467f0f91b8ff9ae9a01682ad3bf">Ref_cm4_simd.txt</a>
</li>
<li>__SHSUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac3ec7215b354d925a239f3b31df2b77b">Ref_cm4_simd.txt</a>
</li>
<li>__SMLAD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae0c86f3298532183f3a29f5bb454d354">Ref_cm4_simd.txt</a>
</li>
<li>__SMLADX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9c286d330f4fb29b256335add91eec9f">Ref_cm4_simd.txt</a>
</li>
<li>__SMLALD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad80e9b20c1736fd798f897362273a146">Ref_cm4_simd.txt</a>
</li>
<li>__SMLALDX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad1adad1b3f2667328cc0db6c6b4f41cf">Ref_cm4_simd.txt</a>
</li>
<li>__SMLSD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaf4350af7f2030c36f43b2c104a9d16cd">Ref_cm4_simd.txt</a>
</li>
<li>__SMLSDX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5290ce5564770ad124910d2583dc0a9e">Ref_cm4_simd.txt</a>
</li>
<li>__SMLSLD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5611f7314e0c8f53da377918dfbf42ee">Ref_cm4_simd.txt</a>
</li>
<li>__SMLSLDX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga83e69ef81057d3cbd06863d729385187">Ref_cm4_simd.txt</a>
</li>
<li>__SMMLA()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaea60757232f740ec6b09980eebb614ff">Ref_cm4_simd.txt</a>
</li>
<li>__SMUAD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gae326e368a1624d2dfb4b97c626939257">Ref_cm4_simd.txt</a>
</li>
<li>__SMUADX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaee6390f86965cb662500f690b0012092">Ref_cm4_simd.txt</a>
</li>
<li>__SMUSD()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga039142a5368840683cf329cb55b73f84">Ref_cm4_simd.txt</a>
</li>
<li>__SMUSDX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gabb5bcba694bf17b141c32e6a8474f60e">Ref_cm4_simd.txt</a>
</li>
<li>__SSAT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga7d9dddda18805abbf51ac21c639845e1">Ref_cmInstr.txt</a>
</li>
<li>__SSAT16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga95e666b82216066bf6064d1244e6883c">Ref_cm4_simd.txt</a>
</li>
<li>__SSAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9d3bc5c539f9bd50f7d59ffa37ac6a65">Ref_cm4_simd.txt</a>
</li>
<li>__SSUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga4262f73be75efbac6b46ab7c71aa6cbc">Ref_cm4_simd.txt</a>
</li>
<li>__SSUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaba63bb52e1e93fb527e26f3d474da12e">Ref_cm4_simd.txt</a>
</li>
<li>__STRBT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gad41aa59c92c0a165b7f98428d3320cd5">Ref_cmInstr.txt</a>
</li>
<li>__STREXB()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gaab6482d1f59f59e2b6b7efc1af391c99">Ref_cmInstr.txt</a>
</li>
<li>__STREXH()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga0a354bdf71caa52f081a4a54e84c8d2a">Ref_cmInstr.txt</a>
</li>
<li>__STREXW()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga335deaaa7991490e1450cb7d1e4c5197">Ref_cmInstr.txt</a>
</li>
<li>__STRHT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga2b5d93b8e461755b1072a03df3f1722e">Ref_cmInstr.txt</a>
</li>
<li>__STRT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga625bc4ac0b1d50de9bcd13d9f050030e">Ref_cmInstr.txt</a>
</li>
<li>__SXTAB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac540b4fc41d30778ba102d2a65db5589">Ref_cm4_simd.txt</a>
</li>
<li>__SXTB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga38dce3dd13ba212e80ec3cff4abeb11a">Ref_cm4_simd.txt</a>
</li>
<li>__UADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaa1160f0cf76d6aa292fbad54a1aa6b74">Ref_cm4_simd.txt</a>
</li>
<li>__UADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab3d7fd00d113b20fb3741a17394da762">Ref_cm4_simd.txt</a>
</li>
<li>__UASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga980353d2c72ebb879282e49f592fddc0">Ref_cm4_simd.txt</a>
</li>
<li>__UHADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gabd0b0e2da2e6364e176d051687702b86">Ref_cm4_simd.txt</a>
</li>
<li>__UHADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga3a14e5485e59bf0f23595b7c2a94eb0b">Ref_cm4_simd.txt</a>
</li>
<li>__UHASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga028f0732b961fb6e5209326fb3855261">Ref_cm4_simd.txt</a>
</li>
<li>__UHSAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga09e129e6613329aab87c89f1108b7ed7">Ref_cm4_simd.txt</a>
</li>
<li>__UHSUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga1f7545b8dc33bb97982731cb9d427a69">Ref_cm4_simd.txt</a>
</li>
<li>__UHSUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga48a55df1c3e73923b73819d7c19b392d">Ref_cm4_simd.txt</a>
</li>
<li>__UQADD16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9e2cc5117e79578a08b25f1e89022966">Ref_cm4_simd.txt</a>
</li>
<li>__UQADD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gafa9af218db3934a692fb06fa728d8031">Ref_cm4_simd.txt</a>
</li>
<li>__UQASX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5eff3ae5eabcd73f3049996ca391becb">Ref_cm4_simd.txt</a>
</li>
<li>__UQSAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gadecfdfabc328d8939d49d996f2fd4482">Ref_cm4_simd.txt</a>
</li>
<li>__UQSUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga5ec4e2e231d15e5c692233feb3806187">Ref_cm4_simd.txt</a>
</li>
<li>__UQSUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9736fe816aec74fe886e7fb949734eab">Ref_cm4_simd.txt</a>
</li>
<li>__USAD8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gac8855c07044239ea775c8128013204f0">Ref_cm4_simd.txt</a>
</li>
<li>__USADA8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad032bd21f013c5d29f5fcb6b0f02bc3f">Ref_cm4_simd.txt</a>
</li>
<li>__USAT()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga76bbe4374a5912362866cdc1ded4064a">Ref_cmInstr.txt</a>
</li>
<li>__USAT16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga967f516afff5900cf30f1a81907cdd89">Ref_cm4_simd.txt</a>
</li>
<li>__USAX()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga578a082747436772c482c96d7a58e45e">Ref_cm4_simd.txt</a>
</li>
<li>__USUB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#ga9f2b77e11fc4a77b26c36c423ed45b4e">Ref_cm4_simd.txt</a>
</li>
<li>__USUB8()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gacb7257dc3b8e9acbd0ef0e31ff87d4b8">Ref_cm4_simd.txt</a>
</li>
<li>__UXTAB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gad25ce96db0f17096bbd815f4817faf09">Ref_cm4_simd.txt</a>
</li>
<li>__UXTB16()
: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gab41d713653b16f8d9fef44d14e397228">Ref_cm4_simd.txt</a>
</li>
<li>__WFE()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gad3efec76c3bfa2b8528ded530386c563">Ref_cmInstr.txt</a>
</li>
<li>__WFI()
: <a class="el" href="group__intrinsic___c_p_u__gr.html#gaed91dfbf3d7d7b7fba8d912fcbeaad88">Ref_cmInstr.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_b"></a>- b -</h3><ul>
<li>BusFault_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a8693500eff174f16119e96234fee73af">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>DebugMonitor_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a8e033fcef7aed98a31c60a7de206722c">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_h"></a>- h -</h3><ul>
<li>HardFault_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ab1a222a34a32f0ef5ac65e714efc1f85">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_i"></a>- i -</h3><ul>
<li>IRQn_Type
: <a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">Ref_NVIC.txt</a>
</li>
<li>ITM_CheckChar()
: <a class="el" href="group___i_t_m___debug__gr.html#ga7f9bbabd9756d1a7eafb2d9bf27e0535">Ref_Debug.txt</a>
</li>
<li>ITM_ReceiveChar()
: <a class="el" href="group___i_t_m___debug__gr.html#ga37b8f41cae703b5ff6947e271065558c">Ref_Debug.txt</a>
</li>
<li>ITM_RxBuffer
: <a class="el" href="group___i_t_m___debug__gr.html#ga12e68e55a7badc271b948d6c7230b2a8">Ref_Debug.txt</a>
</li>
<li>ITM_SendChar()
: <a class="el" href="group___i_t_m___debug__gr.html#gaaa7c716331f74d644bf6bf25cd3392d1">Ref_Debug.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>MemoryManagement_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a33ff1cf7098de65d61b6354fee6cd5aa">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_n"></a>- n -</h3><ul>
<li>NonMaskableInt_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ade177d9c70c89e084093024b932a4e30">Ref_NVIC.txt</a>
</li>
<li>NVIC_ClearPendingIRQ()
: <a class="el" href="group___n_v_i_c__gr.html#ga382ad6bedd6eecfdabd1b94dd128a01a">Ref_NVIC.txt</a>
</li>
<li>NVIC_DecodePriority()
: <a class="el" href="group___n_v_i_c__gr.html#gad3cbca1be7a4726afa9448a9acd89377">Ref_NVIC.txt</a>
</li>
<li>NVIC_DisableIRQ()
: <a class="el" href="group___n_v_i_c__gr.html#ga736ba13a76eb37ef6e2c253be8b0331c">Ref_NVIC.txt</a>
</li>
<li>NVIC_EnableIRQ()
: <a class="el" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f">Ref_NVIC.txt</a>
</li>
<li>NVIC_EncodePriority()
: <a class="el" href="group___n_v_i_c__gr.html#ga0688c59605b119c53c71b2505ab23eb5">Ref_NVIC.txt</a>
</li>
<li>NVIC_GetActive()
: <a class="el" href="group___n_v_i_c__gr.html#gadf4252e600661fd762cfc0d1a9f5b892">Ref_NVIC.txt</a>
</li>
<li>NVIC_GetPendingIRQ()
: <a class="el" href="group___n_v_i_c__gr.html#ga95a8329a680b051ecf3ee8f516acc662">Ref_NVIC.txt</a>
</li>
<li>NVIC_GetPriority()
: <a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395">Ref_NVIC.txt</a>
</li>
<li>NVIC_GetPriorityGrouping()
: <a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78">Ref_NVIC.txt</a>
</li>
<li>NVIC_SetPendingIRQ()
: <a class="el" href="group___n_v_i_c__gr.html#ga3b885147ef9965ecede49614de8df9d2">Ref_NVIC.txt</a>
</li>
<li>NVIC_SetPriority()
: <a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798">Ref_NVIC.txt</a>
</li>
<li>NVIC_SetPriorityGrouping()
: <a class="el" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354">Ref_NVIC.txt</a>
</li>
<li>NVIC_SystemReset()
: <a class="el" href="group___n_v_i_c__gr.html#ga1b47d17e90b6a03e7bd1ec6a0d549b46">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>PendSV_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a03c3cc89984928816d81793fc7bce4a2">Ref_NVIC.txt</a>
</li>
<li>PVD_STM_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a853e0f318108110e0527f29733d11f86">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>SCB_CleanDCache()
: <a class="el" href="group___dcache__functions__m7.html#ga55583e3065c6eabca204b8b89b121c4c">core_cm7.txt</a>
</li>
<li>SCB_CleanDCache_by_Addr()
: <a class="el" href="group___dcache__functions__m7.html#ga696fadbf7b9cc71dad42fab61873a40d">core_cm7.txt</a>
</li>
<li>SCB_CleanInvalidateDCache()
: <a class="el" href="group___dcache__functions__m7.html#ga1b741def9e3b2ca97dc9ea49b8ce505c">core_cm7.txt</a>
</li>
<li>SCB_CleanInvalidateDCache_by_Addr()
: <a class="el" href="group___dcache__functions__m7.html#ga630131b2572eaa16b569ed364dfc895e">core_cm7.txt</a>
</li>
<li>SCB_DisableDCache()
: <a class="el" href="group___dcache__functions__m7.html#ga6468170f90d270caab8116e7a4f0b5fe">core_cm7.txt</a>
</li>
<li>SCB_DisableICache()
: <a class="el" href="group___icache__functions__m7.html#gaba757390852f95b3ac2d8638c717d8d8">core_cm7.txt</a>
</li>
<li>SCB_EnableDCache()
: <a class="el" href="group___dcache__functions__m7.html#ga63aa640d9006021a796a5dcf9c7180b6">core_cm7.txt</a>
</li>
<li>SCB_EnableICache()
: <a class="el" href="group___icache__functions__m7.html#gaf9e7c6c8e16ada1f95e5bf5a03505b68">core_cm7.txt</a>
</li>
<li>SCB_GetFPUType()
: <a class="el" href="group__fpu__functions__m7.html#ga6bcad99ce80a0e7e4ddc6f2379081756">core_cm7.txt</a>
</li>
<li>SCB_InvalidateDCache()
: <a class="el" href="group___dcache__functions__m7.html#gace2d30db08887d0bdb818b8a785a5ce6">core_cm7.txt</a>
</li>
<li>SCB_InvalidateDCache_by_Addr()
: <a class="el" href="group___dcache__functions__m7.html#ga503ef7ef58c0773defd15a82f6336c09">core_cm7.txt</a>
</li>
<li>SCB_InvalidateICache()
: <a class="el" href="group___icache__functions__m7.html#ga50d373a785edd782c5de5a3b55e30ff3">core_cm7.txt</a>
</li>
<li>SVCall_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a4ce820b3cc6cf3a796b41aadc0cf1237">Ref_NVIC.txt</a>
</li>
<li>SystemCoreClock
: <a class="el" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6">Ref_SystemAndClock.txt</a>
</li>
<li>SystemCoreClockUpdate()
: <a class="el" href="group__system__init__gr.html#gae0c36a9591fe6e9c45ecb21a794f0f0f">Ref_SystemAndClock.txt</a>
</li>
<li>SystemInit()
: <a class="el" href="group__system__init__gr.html#ga93f514700ccf00d08dbdcff7f1224eb2">Ref_SystemAndClock.txt</a>
</li>
<li>SysTick_Config()
: <a class="el" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427">Ref_Systick.txt</a>
</li>
<li>SysTick_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6dbff8f8543325f3474cbae2446776e7">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_u"></a>- u -</h3><ul>
<li>UsageFault_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6895237c9443601ac832efa635dd8bbf">Ref_NVIC.txt</a>
</li>
</ul>


<h3><a class="anchor" id="index_w"></a>- w -</h3><ul>
<li>WWDG_STM_IRQn
: <a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8aa62e040960b4beb6cba107e4703c12d2">Ref_NVIC.txt</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
