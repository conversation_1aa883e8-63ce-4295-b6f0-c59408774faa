Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(.text) refers to _printf_str.o(.text) for _printf_str
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(.text) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    main.o(.text) refers to delay.o(.text) for delay_ms
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(.text) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    main.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(.text) refers to sin.o(i.sin) for sin
    main.o(.text) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    main.o(.text) refers to usart.o(.data) for USART_RX_STA
    main.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    main.o(.text) refers to main.o(.data) for test_float
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.sin) for sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.__sin$lsc) for __sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.__sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.__sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.__sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.__sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing led.o(.text), (80 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(.text), (12 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

4 unused section(s) (total 130 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001a4   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000013  0x080001aa   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001b0   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x080001b6   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ba   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001bc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001bc   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001be   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001c0   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001c0   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001c0   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001c0   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001c0   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001c0   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001c2   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001c2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001c2   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001c8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001c8   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001cc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001cc   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001d4   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001d6   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001d6   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001da   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001e0   Section        0  main.o(.text)
    .text                                    0x080004e4   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000500   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000501   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080005d7   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080006e0   Section        0  delay.o(.text)
    .text                                    0x080007b4   Section        0  usart.o(.text)
    .text                                    0x080008f4   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000934   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000c90   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08001034   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x0800143c   Section        0  misc.o(.text)
    .text                                    0x08001518   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800151c   Section        0  noretval__2printf.o(.text)
    .text                                    0x08001534   Section        0  __printf.o(.text)
    .text                                    0x0800159c   Section        0  _printf_str.o(.text)
    .text                                    0x080015f0   Section        0  _printf_dec.o(.text)
    .text                                    0x08001668   Section       68  rt_memclr.o(.text)
    .text                                    0x080016ac   Section        0  heapauxi.o(.text)
    .text                                    0x080016b2   Section        2  use_no_semi.o(.text)
    .text                                    0x080016b4   Section        0  _rserrno.o(.text)
    .text                                    0x080016ca   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800177c   Section        0  _printf_char.o(.text)
    .text                                    0x080017a8   Section        0  _printf_char_file.o(.text)
    .text                                    0x080017cc   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800181c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08001824   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001825   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001854   Section        0  ferror.o(.text)
    .text                                    0x0800185c   Section        8  libspace.o(.text)
    .text                                    0x08001864   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080018ae   Section        0  exit.o(.text)
    i.__ARM_fpclassify                       0x080018ba   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ieee754_rem_pio2                     0x080018e4   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08001c6c   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x08001d68   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x08001e14   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x08001f00   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x08001f06   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08001f14   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.sin                                    0x08001f24   Section        0  sin.o(i.sin)
    x$fpl$d2f                                0x08001fc4   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08002028   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08002039   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08002178   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x08002188   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800218f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x08002438   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08002496   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080024c4   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x080024ec   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002640   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080026dc   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x080026e8   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x08002700   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08002711   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x080028d4   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$ffix                               0x0800292c   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fmul                               0x08002964   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08002a66   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08002af2   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$retnan                             0x08002afc   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08002b60   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08002bbc   Section       48  trapv.o(x$fpl$trapveneer)
    x$fpl$usenofp                            0x08002bec   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08002bf0   Section       48  cos_i.o(.constdata)
    C                                        0x08002bf0   Data          48  cos_i.o(.constdata)
    .constdata                               0x08002c20   Section      200  rred.o(.constdata)
    pio2s                                    0x08002c20   Data          48  rred.o(.constdata)
    twooverpi                                0x08002c50   Data         152  rred.o(.constdata)
    .constdata                               0x08002ce8   Section       40  sin_i.o(.constdata)
    S                                        0x08002ce8   Data          40  sin_i.o(.constdata)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000001c   Section        4  delay.o(.data)
    fac_us                                   0x2000001c   Data           1  delay.o(.data)
    fac_ms                                   0x2000001e   Data           2  delay.o(.data)
    .data                                    0x20000020   Section        6  usart.o(.data)
    .data                                    0x20000026   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000026   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000036   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x2000003c   Section      200  usart.o(.bss)
    .bss                                     0x20000104   Section       96  libspace.o(.bss)
    HEAP                                     0x20000168   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000168   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000368   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000368   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000768   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080001a5   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_c                                0x080001ab   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001b1   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x080001b7   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001bb   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001bd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001bf   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001c1   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001c3   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001c3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001c3   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001c9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001c9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001d5   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001d7   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001d7   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001db   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    HMI_send_string                          0x080001e1   Thumb Code    18  main.o(.text)
    HMI_send_number                          0x080001f3   Thumb Code    18  main.o(.text)
    HMI_send_float                           0x08000205   Thumb Code    38  main.o(.text)
    HMI_Wave                                 0x0800022b   Thumb Code    22  main.o(.text)
    HMI_Wave_Fast                            0x08000241   Thumb Code    60  main.o(.text)
    HMI_Wave_Clear                           0x0800027d   Thumb Code    18  main.o(.text)
    main                                     0x0800028f   Thumb Code   426  main.o(.text)
    NMI_Handler                              0x080004e5   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x080004e7   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x080004eb   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x080004ef   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x080004f3   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x080004f7   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x080004f9   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x080004fb   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x080004fd   Thumb Code     2  stm32f10x_it.o(.text)
    SystemInit                               0x080005df   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800062d   Thumb Code   142  system_stm32f10x.o(.text)
    delay_init                               0x080006e1   Thumb Code    50  delay.o(.text)
    delay_us                                 0x08000713   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x0800075b   Thumb Code    72  delay.o(.text)
    _sys_exit                                0x080007b5   Thumb Code     6  usart.o(.text)
    fputc                                    0x080007bb   Thumb Code    24  usart.o(.text)
    uart_init                                0x080007d3   Thumb Code   152  usart.o(.text)
    USART1_IRQHandler                        0x0800086b   Thumb Code   122  usart.o(.text)
    Reset_Handler                            0x080008f5   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800090f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000911   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    GPIO_DeInit                              0x08000935   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x080009e1   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x080009f5   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x08000b0b   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000b1b   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08000b2d   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000b35   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000b47   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08000b4f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08000b53   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08000b57   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08000b61   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000b65   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000b77   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08000b91   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08000b97   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08000c21   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08000c63   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08000c91   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08000cd1   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000d17   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000d4f   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000d87   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000d9b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08000da1   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000db9   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000dbf   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000dd1   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08000ddb   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08000ded   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08000dff   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08000e13   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08000e2d   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08000e35   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08000e47   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000e79   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000e7f   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000e8b   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000e93   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08000f53   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000f6d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000f87   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000fa1   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000fbb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000fd5   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000fdd   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08000fe3   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08000fe9   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08000ff7   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800100b   Thumb Code     6  stm32f10x_rcc.o(.text)
    USART_DeInit                             0x08001035   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x080010bb   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x0800118d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x080011a5   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x080011c7   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x080011d3   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x080011eb   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08001235   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08001247   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08001259   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x0800126b   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001283   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08001295   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x080012ad   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x080012b5   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x080012bf   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x080012c9   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x080012d9   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x080012e9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001301   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001319   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001331   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001347   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x0800135f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08001371   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08001389   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x080013a3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x080013b5   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001409   Thumb Code    52  stm32f10x_usart.o(.text)
    NVIC_PriorityGroupConfig                 0x0800143d   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08001447   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x080014ab   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080014b9   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080014db   Thumb Code    40  misc.o(.text)
    __use_no_semihosting                     0x08001519   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800151d   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x08001535   Thumb Code   104  __printf.o(.text)
    _printf_str                              0x0800159d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080015f1   Thumb Code   104  _printf_dec.o(.text)
    __aeabi_memclr                           0x08001669   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08001669   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800166d   Thumb Code     0  rt_memclr.o(.text)
    __use_two_region_memory                  0x080016ad   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080016af   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080016b1   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080016b3   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080016b3   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x080016b5   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080016bf   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080016cb   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_cs_common                        0x0800177d   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001791   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080017a1   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x080017a9   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_memclr4                          0x080017cd   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080017cd   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080017cd   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080017d1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_errno_addr                       0x0800181d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800181d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800181d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x0800182f   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08001855   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x0800185d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800185d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800185d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001865   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080018af   Thumb Code    12  exit.o(.text)
    __ARM_fpclassify                         0x080018bb   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __ieee754_rem_pio2                       0x080018e5   Thumb Code   828  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08001c6d   Thumb Code   230  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x08001d69   Thumb Code   170  poly.o(i.__kernel_poly)
    __kernel_sin                             0x08001e15   Thumb Code   224  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x08001f01   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x08001f07   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08001f15   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    sin                                      0x08001f25   Thumb Code   150  sin.o(i.sin)
    __aeabi_d2f                              0x08001fc5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08001fc5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08002029   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08002029   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08002179   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x08002189   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002189   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x08002439   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08002439   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08002497   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08002497   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080024c5   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080024c5   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x080024ed   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080024ed   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002641   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080026dd   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x080026e9   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x080026e9   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x08002701   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08002701   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x080028d5   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080028d5   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_f2iz                             0x0800292d   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x0800292d   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_fmul                             0x08002965   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08002965   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08002a67   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08002af3   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __fpl_return_NaN                         0x08002afd   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08002b61   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08002bbd   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08002bec   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002d10   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002d30   Number         0  anon$$obj.o(Region$$Table)
    test_num                                 0x20000000   Data           4  main.o(.data)
    test_float                               0x20000004   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000020   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000024   Data           2  usart.o(.data)
    USART_RX_BUF                             0x2000003c   Data         200  usart.o(.bss)
    __libspace_start                         0x20000104   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000164   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002d6c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00002d30, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO          265    RESET               startup_stm32f10x_hd.o
    0x08000130   0x00000008   Code   RO          362  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO          629    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO          631    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO          633    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000000   Code   RO          357    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x00000006   Code   RO          356    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001aa   0x00000006   Code   RO          354    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001b0   0x00000006   Code   RO          355    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001b6   0x00000004   Code   RO          415    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ba   0x00000002   Code   RO          501    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001bc   0x00000000   Code   RO          507    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          509    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          512    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          514    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          516    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          519    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          521    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          523    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          525    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          527    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          529    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          531    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          533    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          535    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          537    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          539    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          543    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          545    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          547    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001bc   0x00000000   Code   RO          549    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001bc   0x00000002   Code   RO          550    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001be   0x00000002   Code   RO          572    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001c0   0x00000000   Code   RO          583    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001c0   0x00000000   Code   RO          586    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001c0   0x00000000   Code   RO          589    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001c0   0x00000000   Code   RO          591    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001c0   0x00000000   Code   RO          594    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001c0   0x00000002   Code   RO          595    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001c2   0x00000000   Code   RO          406    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001c2   0x00000000   Code   RO          456    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001c2   0x00000006   Code   RO          468    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001c8   0x00000000   Code   RO          458    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001c8   0x00000004   Code   RO          459    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001cc   0x00000000   Code   RO          461    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001cc   0x00000008   Code   RO          462    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001d4   0x00000002   Code   RO          504    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001d6   0x00000000   Code   RO          554    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001d6   0x00000004   Code   RO          555    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001da   0x00000006   Code   RO          556    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001e0   0x00000304   Code   RO            1    .text               main.o
    0x080004e4   0x0000001a   Code   RO          125    .text               stm32f10x_it.o
    0x080004fe   0x00000002   PAD
    0x08000500   0x000001e0   Code   RO          166    .text               system_stm32f10x.o
    0x080006e0   0x000000d4   Code   RO          199    .text               delay.o
    0x080007b4   0x00000140   Code   RO          230    .text               usart.o
    0x080008f4   0x00000040   Code   RO          266    .text               startup_stm32f10x_hd.o
    0x08000934   0x0000035c   Code   RO          270    .text               stm32f10x_gpio.o
    0x08000c90   0x000003a4   Code   RO          282    .text               stm32f10x_rcc.o
    0x08001034   0x00000408   Code   RO          296    .text               stm32f10x_usart.o
    0x0800143c   0x000000dc   Code   RO          308    .text               misc.o
    0x08001518   0x00000002   Code   RO          322    .text               c_w.l(use_no_semi_2.o)
    0x0800151a   0x00000002   PAD
    0x0800151c   0x00000018   Code   RO          326    .text               c_w.l(noretval__2printf.o)
    0x08001534   0x00000068   Code   RO          328    .text               c_w.l(__printf.o)
    0x0800159c   0x00000052   Code   RO          330    .text               c_w.l(_printf_str.o)
    0x080015ee   0x00000002   PAD
    0x080015f0   0x00000078   Code   RO          332    .text               c_w.l(_printf_dec.o)
    0x08001668   0x00000044   Code   RO          358    .text               c_w.l(rt_memclr.o)
    0x080016ac   0x00000006   Code   RO          360    .text               c_w.l(heapauxi.o)
    0x080016b2   0x00000002   Code   RO          404    .text               c_w.l(use_no_semi.o)
    0x080016b4   0x00000016   Code   RO          407    .text               c_w.l(_rserrno.o)
    0x080016ca   0x000000b2   Code   RO          409    .text               c_w.l(_printf_intcommon.o)
    0x0800177c   0x0000002c   Code   RO          411    .text               c_w.l(_printf_char.o)
    0x080017a8   0x00000024   Code   RO          413    .text               c_w.l(_printf_char_file.o)
    0x080017cc   0x0000004e   Code   RO          416    .text               c_w.l(rt_memclr_w.o)
    0x0800181a   0x00000002   PAD
    0x0800181c   0x00000008   Code   RO          473    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08001824   0x00000030   Code   RO          475    .text               c_w.l(_printf_char_common.o)
    0x08001854   0x00000008   Code   RO          477    .text               c_w.l(ferror.o)
    0x0800185c   0x00000008   Code   RO          487    .text               c_w.l(libspace.o)
    0x08001864   0x0000004a   Code   RO          490    .text               c_w.l(sys_stackheap_outer.o)
    0x080018ae   0x0000000c   Code   RO          494    .text               c_w.l(exit.o)
    0x080018ba   0x00000028   Code   RO          483    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x080018e2   0x00000002   PAD
    0x080018e4   0x00000388   Code   RO          444    i.__ieee754_rem_pio2  m_ws.l(rred.o)
    0x08001c6c   0x000000fc   Code   RO          427    i.__kernel_cos      m_ws.l(cos_i.o)
    0x08001d68   0x000000aa   Code   RO          485    i.__kernel_poly     m_ws.l(poly.o)
    0x08001e12   0x00000002   PAD
    0x08001e14   0x000000ec   Code   RO          449    i.__kernel_sin      m_ws.l(sin_i.o)
    0x08001f00   0x00000006   Code   RO          431    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x08001f06   0x0000000c   Code   RO          433    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x08001f12   0x00000002   PAD
    0x08001f14   0x00000010   Code   RO          436    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08001f24   0x000000a0   Code   RO          397    i.sin               m_ws.l(sin.o)
    0x08001fc4   0x00000062   Code   RO          364    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08002026   0x00000002   PAD
    0x08002028   0x00000150   Code   RO          366    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08002178   0x00000010   Code   RO          502    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08002188   0x000002b0   Code   RO          373    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002438   0x0000005e   Code   RO          376    x$fpl$dfix          fz_ws.l(dfix.o)
    0x08002496   0x0000002e   Code   RO          381    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x080024c4   0x00000026   Code   RO          380    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080024ea   0x00000002   PAD
    0x080024ec   0x00000154   Code   RO          386    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002640   0x0000009c   Code   RO          418    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080026dc   0x0000000c   Code   RO          420    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080026e8   0x00000016   Code   RO          367    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x080026fe   0x00000002   PAD
    0x08002700   0x000001d4   Code   RO          368    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x080028d4   0x00000056   Code   RO          388    x$fpl$f2d           fz_ws.l(f2d.o)
    0x0800292a   0x00000002   PAD
    0x0800292c   0x00000036   Code   RO          390    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08002962   0x00000002   PAD
    0x08002964   0x00000102   Code   RO          394    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08002a66   0x0000008c   Code   RO          422    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08002af2   0x0000000a   Code   RO          424    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002afc   0x00000064   Code   RO          551    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08002b60   0x0000005c   Code   RO          481    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08002bbc   0x00000030   Code   RO          564    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08002bec   0x00000000   Code   RO          426    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08002bec   0x00000004   PAD
    0x08002bf0   0x00000030   Data   RO          428    .constdata          m_ws.l(cos_i.o)
    0x08002c20   0x000000c8   Data   RO          446    .constdata          m_ws.l(rred.o)
    0x08002ce8   0x00000028   Data   RO          450    .constdata          m_ws.l(sin_i.o)
    0x08002d10   0x00000020   Data   RO          627    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000768, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000008   Data   RW            2    .data               main.o
    0x20000008   0x00000014   Data   RW          167    .data               system_stm32f10x.o
    0x2000001c   0x00000004   Data   RW          200    .data               delay.o
    0x20000020   0x00000006   Data   RW          232    .data               usart.o
    0x20000026   0x00000014   Data   RW          283    .data               stm32f10x_rcc.o
    0x2000003a   0x00000002   PAD
    0x2000003c   0x000000c8   Zero   RW          231    .bss                usart.o
    0x20000104   0x00000060   Zero   RW          488    .bss                c_w.l(libspace.o)
    0x20000164   0x00000004   PAD
    0x20000168   0x00000200   Zero   RW          264    HEAP                startup_stm32f10x_hd.o
    0x20000368   0x00000400   Zero   RW          263    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       212         18          0          4          0       1023   delay.o
       772        172          0          8          0     233346   main.o
       220         22          0          0          0       1801   misc.o
        64         26        304          0       1536        792   startup_stm32f10x_hd.o
       860         38          0          0          0       5645   stm32f10x_gpio.o
        26          0          0          0          0       1178   stm32f10x_it.o
       932         36          0         20          0       8812   stm32f10x_rcc.o
      1032         22          0          0          0       8368   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       480         38          0         20          0       1739   system_stm32f10x.o
       320         16          0          6        200       3146   usart.o

    ----------------------------------------------------------------------
      4920        <USER>        <GROUP>         60       1736     265882   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        22          0          0          0          0        100   _rserrno.o
        12          0          0          0          0         72   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
        94          4          0          0          0         92   dfix.o
        84          0          0          0          0        136   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
        54          4          0          0          0         84   ffix.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       252         22         48          0          0        124   cos_i.o
        34          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
       904         76        200          0          0        140   rred.o
       160         10          0          0          0        108   sin.o
       236         12         40          0          0        128   sin_i.o

    ----------------------------------------------------------------------
      6024        <USER>        <GROUP>          0        100       4356   Library Totals
        28          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1098         46          0          0         96       1672   c_w.l
      3102        200          0          0          0       1816   fz_ws.l
      1796        126        288          0          0        868   m_ws.l

    ----------------------------------------------------------------------
      6024        <USER>        <GROUP>          0        100       4356   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10944        760        624         60       1836     267218   Grand Totals
     10944        760        624         60       1836     267218   ELF Image Totals
     10944        760        624         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11568 (  11.30kB)
    Total RW  Size (RW Data + ZI Data)              1896 (   1.85kB)
    Total ROM Size (Code + RO Data + RW Data)      11628 (  11.36kB)

==============================================================================

