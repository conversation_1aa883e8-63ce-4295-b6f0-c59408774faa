<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>I-Cache Functions</title>
<title>CMSIS-CORE: I-Cache Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___icache__functions__m7.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">I-Cache Functions</div>  </div>
<div class="ingroups"><a class="el" href="group__cache__functions__m7.html">Cache Functions  (only Cortex-M7)</a></div></div><!--header-->
<div class="contents">

<p>Functions for the instruction cache.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf9e7c6c8e16ada1f95e5bf5a03505b68"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___icache__functions__m7.html#gaf9e7c6c8e16ada1f95e5bf5a03505b68">SCB_EnableICache</a> (void)</td></tr>
<tr class="memdesc:gaf9e7c6c8e16ada1f95e5bf5a03505b68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable I-Cache.  <a href="#gaf9e7c6c8e16ada1f95e5bf5a03505b68"></a><br/></td></tr>
<tr class="separator:gaf9e7c6c8e16ada1f95e5bf5a03505b68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba757390852f95b3ac2d8638c717d8d8"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___icache__functions__m7.html#gaba757390852f95b3ac2d8638c717d8d8">SCB_DisableICache</a> (void)</td></tr>
<tr class="memdesc:gaba757390852f95b3ac2d8638c717d8d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable I-Cache.  <a href="#gaba757390852f95b3ac2d8638c717d8d8"></a><br/></td></tr>
<tr class="separator:gaba757390852f95b3ac2d8638c717d8d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50d373a785edd782c5de5a3b55e30ff3"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___icache__functions__m7.html#ga50d373a785edd782c5de5a3b55e30ff3">SCB_InvalidateICache</a> (void)</td></tr>
<tr class="memdesc:ga50d373a785edd782c5de5a3b55e30ff3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invalidate I-Cache.  <a href="#ga50d373a785edd782c5de5a3b55e30ff3"></a><br/></td></tr>
<tr class="separator:ga50d373a785edd782c5de5a3b55e30ff3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaba757390852f95b3ac2d8638c717d8d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_DisableICache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function turns off the instruction cache. </p>

</div>
</div>
<a class="anchor" id="gaf9e7c6c8e16ada1f95e5bf5a03505b68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_EnableICache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function turns on the instruction cache. </p>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>Before enabling the instruction cache, you must invalidate (<a class="el" href="group___icache__functions__m7.html#ga50d373a785edd782c5de5a3b55e30ff3">SCB_InvalidateICache</a>) the entire instruction cache if external memory might have changed since the cache was disabled. </li>
</ul>
</dd>
<dd>
<ul>
<li>After reset, you must invalidate (<a class="el" href="group___icache__functions__m7.html#ga50d373a785edd782c5de5a3b55e30ff3">SCB_InvalidateICache</a>) each cache before enabling it. </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga50d373a785edd782c5de5a3b55e30ff3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_InvalidateICache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function invalidates the instruction cache. The instruction cache is never dirty so cache RAM errors are always recoverable by invalidating the cache and retrying the instruction. </p>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>After reset, you must invalidate each cache before enabling (<a class="el" href="group___icache__functions__m7.html#gaf9e7c6c8e16ada1f95e5bf5a03505b68">SCB_EnableICache</a>) it. </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
